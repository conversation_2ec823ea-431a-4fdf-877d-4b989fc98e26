#!/usr/bin/env python3
"""
简单的whisper.cpp转录器测试
直接测试命令行调用，不依赖复杂的模块导入
"""

import os
import sys
import subprocess
import json
import tempfile
from pathlib import Path

def test_whisper_cli():
    """测试whisper-cli命令行工具"""
    print("=== 测试whisper-cli工具 ===")
    
    # 检查whisper-cli
    whisper_cli = Path("whisper.cpp/build/bin/whisper-cli")
    if not whisper_cli.exists():
        print(f"✗ whisper-cli未找到: {whisper_cli}")
        return False
    
    print(f"✓ 找到whisper-cli: {whisper_cli}")
    
    # 检查模型
    model_path = Path("whisper.cpp/models/ggml-large-v3-turbo.bin")
    if not model_path.exists():
        print(f"✗ 模型文件未找到: {model_path}")
        return False
    
    print(f"✓ 找到模型文件: {model_path}")
    
    return True

def test_gpu():
    """测试GPU支持"""
    print("\n=== 测试GPU支持 ===")
    
    try:
        result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total', '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            gpu_info = result.stdout.strip().split('\n')
            print(f"✓ 检测到 {len(gpu_info)} 个GPU:")
            for i, info in enumerate(gpu_info):
                name, memory = info.split(', ')
                print(f"  GPU {i}: {name} ({int(memory)/1024:.1f}GB)")
            return True
        else:
            print("✗ 未检测到NVIDIA GPU")
            return False
            
    except FileNotFoundError:
        print("✗ nvidia-smi未找到")
        return False
    except Exception as e:
        print(f"✗ GPU检查失败: {e}")
        return False

def test_transcription():
    """测试音频转录"""
    print("\n=== 测试音频转录 ===")
    
    # 查找测试音频文件
    test_files = [
        "data/samples/short_test_audio.wav",
        "data/samples/过量（二）.mp3"
    ]
    
    test_file = None
    for audio_file in test_files:
        if Path(audio_file).exists():
            test_file = audio_file
            break
    
    if not test_file:
        print("✗ 未找到测试音频文件")
        print("请确保以下文件之一存在:")
        for f in test_files:
            print(f"  - {f}")
        return False
    
    print(f"使用测试文件: {test_file}")
    
    # 构建whisper-cli命令
    whisper_cli = Path("whisper.cpp/build/bin/whisper-cli")
    model_path = Path("whisper.cpp/models/ggml-large-v3-turbo.bin")
    
    # 创建临时输出文件
    with tempfile.NamedTemporaryFile(mode='w+', suffix='.json', delete=False) as temp_file:
        temp_output = temp_file.name
    
    try:
        cmd = [
            str(whisper_cli),
            '-m', str(model_path),
            '-f', test_file,
            '-ng',  # 使用GPU
            '-l', 'ar',  # 阿拉伯语
            '-ml', '1',  # 启用词级时间戳
            '-oj', temp_output  # JSON输出
        ]
        
        print("执行命令:")
        print(" ".join(cmd))
        
        # 执行转录
        print("开始转录...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode != 0:
            print(f"✗ 转录失败，返回码: {result.returncode}")
            print(f"stderr: {result.stderr}")
            return False
        
        # 读取结果
        if Path(temp_output).exists():
            with open(temp_output, 'r', encoding='utf-8') as f:
                transcription_result = json.load(f)
            
            print("✓ 转录成功!")
            
            # 显示结果
            if "transcription" in transcription_result:
                transcription = transcription_result["transcription"]
                if transcription:
                    text = transcription[0].get("text", "").strip()
                    print(f"转录文本: {text[:100]}...")
                    
                    # 检查时间戳
                    if "timestamps" in transcription[0]:
                        print("✓ 包含时间戳信息")
                    else:
                        print("⚠ 未包含时间戳信息")
                else:
                    print("⚠ 转录结果为空")
            
            # 保存结果到输出目录
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)
            output_file = output_dir / "whisper_cpp_test_result.json"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(transcription_result, f, ensure_ascii=False, indent=2)
            
            print(f"结果已保存到: {output_file}")
            return True
        else:
            print("✗ 输出文件未生成")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 转录超时")
        return False
    except Exception as e:
        print(f"✗ 转录失败: {e}")
        return False
    finally:
        # 清理临时文件
        if Path(temp_output).exists():
            os.unlink(temp_output)

def main():
    """主测试函数"""
    print("Whisper.cpp 简单测试")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # 测试1: CLI工具
    if test_whisper_cli():
        tests_passed += 1
    
    # 测试2: GPU支持
    if test_gpu():
        tests_passed += 1
    
    # 测试3: 转录功能
    if test_transcription():
        tests_passed += 1
    
    # 显示结果
    print("\n" + "=" * 50)
    print("测试结果摘要")
    print("=" * 50)
    print(f"通过测试: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✓ 所有测试通过！whisper.cpp工作正常")
        return 0
    else:
        print("✗ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
