# Audio Processor Project .gitignore

# ===== Python =====
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
audio_processor_env/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ===== IDE / Editor =====
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== Operating System =====
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== Project Specific =====
# Model files (large binary files)
*.gguf
*.bin
*.safetensors
*.pt
*.pth
*.onnx
*.tflite
*.h5
*.pkl
*.pickle

# Audio files
*.wav
*.mp3
*.flac
*.m4a
*.ogg
*.aac
*.wma

# Video files
*.mp4
*.avi
*.mkv
*.mov
*.wmv
*.flv

# Image files (if not needed for docs)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.svg
*.ico

# Log files
logs/
*.log
*.log.*

# Output files
output/
results/
temp/
tmp/
cache/

# Configuration files with sensitive data
config_local.yaml
config_private.yaml
.env.local
.env.production
secrets.yaml

# Backup files
*.bak
*.backup
*.old
*.orig

# Compressed files
*.zip
*.tar.gz
*.tar.bz2
*.tar.xz
*.rar
*.7z

# Database files
*.db
*.sqlite
*.sqlite3

# ===== Machine Learning / AI =====
# Weights & Biases
wandb/

# MLflow
mlruns/

# TensorBoard
runs/
tensorboard_logs/

# Jupyter checkpoints
.ipynb_checkpoints/

# Model checkpoints
checkpoints/
*.ckpt

# Dataset cache
.cache/
dataset_cache/

# Hugging Face cache
.cache/huggingface/

# ===== Build / Compilation =====
# CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
*.cmake

# Make
*.o
*.a
*.la
*.lo
*.so.*

# Autotools
.deps/
.libs/
*.la
*.lo
Makefile.in
aclocal.m4
autom4te.cache/
config.guess
config.sub
configure
depcomp
install-sh
ltmain.sh
missing

# ===== Documentation =====
# Sphinx build
_build/
_static/
_templates/

# LaTeX
*.aux
*.bbl
*.blg
*.fdb_latexmk
*.fls
*.idx
*.ilg
*.ind
*.lof
*.lot
*.out
*.toc
*.synctex.gz

# ===== Testing =====
# Coverage reports
htmlcov/
.coverage
coverage.xml

# Test outputs
test_output/
test_results/

# Benchmark results
benchmark_results.json
performance_*.json

# ===== Temporary / Runtime =====
# Process IDs
*.pid

# Lock files
*.lock

# Temporary directories
tmp/
temp/
.tmp/

# Runtime data
runtime/
run/

# ===== Custom Project Exclusions =====
# Large model files should be downloaded separately
audio_processor/models/*/Qwen*.gguf
audio_processor/models/*/whisper*.pt
audio_processor/models/*/pytorch_model.bin

# User-specific test files
my_test_*
user_test_*
personal_*

# Local configuration overrides
local_config.yaml
dev_config.yaml

# Output from example runs
example_output/
demo_results/

# Profiling data
*.prof
*.profile

# Memory dumps
*.dump
core.*
