"""
Audio Processor - 多模态音频处理工具包

支持音频转录、文本处理、多模型集成等功能
"""

__version__ = "1.0.0"
__author__ = "Audio Processor Team"

# 导入主要组件 - 仅音频转录相关
from .transcribing import WhisperTranscriber
from utils.monitor import SystemMonitor
from core.config import ConfigManager

# 注意：Qwen 模块已移动到独立的 qwen 包中
# 使用方法：from qwen import QwenProcessor

__all__ = [
    "WhisperTranscriber",
    "SystemMonitor",
    "ConfigManager",
]
