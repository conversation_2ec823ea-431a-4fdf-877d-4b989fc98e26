"""
Transcribing 模块 - 音频转录功能
"""

# 延迟导入以避免依赖问题
def _get_whisper_cpp_transcriber():
    from .whisper_cpp_transcriber import WhisperCppTranscriber
    return WhisperCppTranscriber

def _get_original_whisper_transcriber():
    from .whisper_original import OriginalWhisperTranscriber
    return OriginalWhisperTranscriber

def _get_huggingface_whisper_transcriber():
    from .transcriber import WhisperTranscriber as HuggingFaceWhisperTranscriber
    return HuggingFaceWhisperTranscriber

# 使用whisper.cpp作为默认转录器
WhisperTranscriber = _get_whisper_cpp_transcriber()

# 为了向后兼容，提供其他转录器的访问方式
def get_transcriber(backend="whisper_cpp"):
    """获取指定后端的转录器"""
    if backend == "whisper_cpp":
        return _get_whisper_cpp_transcriber()
    elif backend == "original":
        return _get_original_whisper_transcriber()
    elif backend == "huggingface":
        return _get_huggingface_whisper_transcriber()
    else:
        raise ValueError(f"未知的转录器后端: {backend}")

__all__ = [
    "WhisperTranscriber",
    "get_transcriber"
]
