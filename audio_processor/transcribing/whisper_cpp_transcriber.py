"""
Whisper.cpp 转录器模块
基于 whisper.cpp 的高性能音频转录工具，支持GPU加速和词级别时间戳
"""

import os
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Union

from loguru import logger

from core.base import BaseProcessor
from utils.monitor import SystemMonitor, ModelMonitorMixin, ModelType, ModelStatus
from utils.device import get_device_manager
from ..preprocessing.processor import AudioPreprocessor


class WhisperCppTranscriber(BaseProcessor, ModelMonitorMixin):
    """Whisper.cpp 音频转录器"""

    def __init__(self, config_name: str = "whisper", enable_preprocessing: Optional[bool] = None):
        """初始化转录器

        Args:
            config_name: 配置名称
            enable_preprocessing: 是否启用预处理（None表示使用配置文件设置）
        """
        super().__init__(config_name)

        # 初始化监控器
        self.monitor = SystemMonitor(self.config)

        # 设置模型监控
        self._setup_model_monitoring(self.monitor, ModelType.WHISPER)
        self._update_model_status(ModelStatus.LOADING)

        # 初始化设备管理器
        self.device_manager = get_device_manager(self.module_logger)
        self.device = self.device_manager.get_preferred_device()
        self.device_manager.log_device_summary()

        # 初始化预处理器
        self.preprocessor: Optional[AudioPreprocessor] = None
        self.enable_preprocessing = enable_preprocessing
        self._init_preprocessor()

        # 初始化whisper.cpp模型
        self.model = None
        self._load_model()

        # 更新模型状态
        self._update_model_status(ModelStatus.LOADED)

    def _init_preprocessor(self):
        """初始化预处理器"""
        # 确定是否启用预处理
        if self.enable_preprocessing is None:
            self.enable_preprocessing = self.get_config_value("preprocessing.enable", False)

        if self.enable_preprocessing:
            self.module_logger.info("启用音频预处理")
            self.preprocessor = AudioPreprocessor("preprocessing")
        else:
            self.module_logger.info("禁用音频预处理")

    def _load_model(self):
        """加载whisper.cpp模型"""
        try:
            model_config = self.get_config_value("model", {})
            model_name = model_config.get("name", "large-v3-turbo")

            # 构建模型路径
            model_path = self._get_model_path(model_name)

            self.module_logger.info(f"正在加载whisper.cpp模型: {model_path}")

            # 检查whisper-cli可执行文件
            whisper_cli_path = self._get_whisper_cli_path()
            if not whisper_cli_path.exists():
                raise FileNotFoundError(f"whisper-cli未找到: {whisper_cli_path}")

            # 存储模型路径和CLI路径
            self.model_path = model_path
            self.whisper_cli_path = whisper_cli_path

            # 检查GPU支持
            self.use_gpu = self.device == "cuda" and self._check_gpu_support()

            self.module_logger.info(f"whisper.cpp模型加载完成 (GPU: {self.use_gpu})")

        except Exception as e:
            self.module_logger.error(f"whisper.cpp模型加载失败: {e}")
            raise

    def _get_model_path(self, model_name: str) -> Path:
        """获取模型文件路径"""
        # whisper.cpp模型文件映射
        model_mapping = {
            "tiny": "ggml-tiny.bin",
            "tiny.en": "ggml-tiny.en.bin",
            "base": "ggml-base.bin", 
            "base.en": "ggml-base.en.bin",
            "small": "ggml-small.bin",
            "small.en": "ggml-small.en.bin", 
            "medium": "ggml-medium.bin",
            "medium.en": "ggml-medium.en.bin",
            "large": "ggml-large.bin",
            "large-v1": "ggml-large-v1.bin",
            "large-v2": "ggml-large-v2.bin", 
            "large-v3": "ggml-large-v3.bin",
            "large-v3-turbo": "ggml-large-v3-turbo.bin"
        }
        
        model_file = model_mapping.get(model_name, f"ggml-{model_name}.bin")
        
        # 检查多个可能的模型路径
        possible_paths = [
            Path("whisper.cpp/models") / model_file,
            Path("models") / model_file,
            Path(".") / model_file,
            Path(f"~/.cache/whisper.cpp/{model_file}").expanduser()
        ]
        
        for path in possible_paths:
            if path.exists():
                return path
                
        # 如果模型不存在，提供下载提示
        self.module_logger.error(f"模型文件未找到: {model_file}")
        self.module_logger.error("请下载模型文件，例如:")
        self.module_logger.error(f"wget https://huggingface.co/ggerganov/whisper.cpp/resolve/main/{model_file}")
        raise FileNotFoundError(f"模型文件未找到: {model_file}")

    def _get_whisper_cli_path(self) -> Path:
        """获取whisper-cli可执行文件路径"""
        possible_paths = [
            Path("bin/whisper-cli"),
            Path("whisper.cpp/build/bin/whisper-cli"),
            Path("whisper.cpp/build/whisper-cli"),
            Path("/usr/local/bin/whisper-cli"),
            Path("./whisper-cli")
        ]

        for path in possible_paths:
            if path.exists():
                return path

        raise FileNotFoundError("whisper-cli可执行文件未找到")

    def _check_gpu_support(self) -> bool:
        """检查GPU支持"""
        try:
            import torch
            return torch.cuda.is_available()
        except Exception:
            return False

    def _build_transcription_params(self, **kwargs) -> Dict[str, Any]:
        """构建转录参数"""
        # 获取配置中的转录参数
        config_params = self.get_config_value("transcription", {})
        
        # 构建whisper.cpp转录参数
        params = {}
        
        # 基本参数
        params["language"] = kwargs.get("language") or config_params.get("language")
        params["task"] = kwargs.get("task", config_params.get("task", "transcribe"))
        params["initial_prompt"] = kwargs.get("initial_prompt") or config_params.get("initial_prompt")
        
        # 解码参数
        params["temperature"] = kwargs.get("temperature", config_params.get("temperature", 0.0))
        params["no_speech_threshold"] = kwargs.get("no_speech_threshold", config_params.get("no_speech_threshold", 0.6))
        params["logprob_threshold"] = kwargs.get("logprob_threshold", config_params.get("logprob_threshold", -1.0))
        params["compression_ratio_threshold"] = kwargs.get("compression_ratio_threshold", config_params.get("compression_ratio_threshold", 2.4))
        
        # 时间戳参数
        params["word_timestamps"] = kwargs.get("word_timestamps", config_params.get("word_timestamps", True))
        
        # 过滤None值
        params = {k: v for k, v in params.items() if v is not None}
        
        self.module_logger.debug(f"构建的转录参数: {params}")
        return params

    def process(self, audio_path: str, **kwargs) -> Dict[str, Any]:
        """处理音频文件进行转录

        Args:
            audio_path: 音频文件路径
            **kwargs: 其他参数

        Returns:
            转录结果字典
        """
        return self.transcribe(audio_path, **kwargs)

    def transcribe(self, audio_path: str, **kwargs) -> Dict[str, Any]:
        """转录音频文件

        Args:
            audio_path: 音频文件路径
            **kwargs: 转录参数

        Returns:
            转录结果字典
        """
        if not hasattr(self, 'model_path'):
            raise RuntimeError("模型未加载")

        audio_path_obj = Path(audio_path)
        if not audio_path_obj.exists():
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")

        self.module_logger.info(f"开始转录音频文件: {audio_path}")
        start_time = time.time()

        try:
            # 根据是否启用预处理选择转录方法
            if self.enable_preprocessing and self.preprocessor:
                result = self._transcribe_with_preprocessing(audio_path, **kwargs)
            else:
                result = self._transcribe_without_preprocessing(audio_path, **kwargs)

            # 计算处理时间
            processing_time = time.time() - start_time
            result["processing_time"] = processing_time

            self.module_logger.info(f"转录完成，耗时: {processing_time:.2f}秒")
            return result

        except Exception as e:
            self.module_logger.error(f"转录失败: {e}")
            raise

    def _transcribe_without_preprocessing(self, audio_path: str, **kwargs) -> Dict[str, Any]:
        """不使用预处理的转录方法"""
        import subprocess
        import json
        import tempfile

        start_time = time.time()

        # 执行转录
        self.module_logger.info("正在执行转录（无预处理）...")

        # 构建whisper-cli命令
        cmd = self._build_whisper_cli_command(audio_path, **kwargs)
        self.module_logger.debug(f"执行命令: {' '.join(cmd)}")

        # 使用whisper-cli转录
        try:
            # 创建临时文件保存输出
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.json', delete=False) as temp_file:
                temp_output_path = temp_file.name

            # 添加输出文件参数
            cmd.extend(['-oj', temp_output_path])

            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)

            # 读取JSON输出
            with open(temp_output_path, 'r', encoding='utf-8') as f:
                whisper_result = json.load(f)

            # 清理临时文件
            os.unlink(temp_output_path)

        except subprocess.CalledProcessError as e:
            self.module_logger.error(f"whisper-cli执行失败: {e}")
            self.module_logger.error(f"stderr: {e.stderr}")
            raise
        except Exception as e:
            self.module_logger.error(f"whisper.cpp转录失败: {e}")
            raise

        # 处理结果
        processing_time = time.time() - start_time

        return {
            "text": whisper_result.get("transcription", [{}])[0].get("text", "") if whisper_result.get("transcription") else "",
            "segments": self._parse_whisper_segments(whisper_result),
            "language": whisper_result.get("language", "unknown"),
            "processing_time": processing_time,
            "model_info": {
                "name": "whisper.cpp",
                "backend": "whisper.cpp",
                "device": self.device
            }
        }

    def _build_whisper_cli_command(self, audio_path: str, **kwargs) -> List[str]:
        """构建whisper-cli命令"""
        cmd = [str(self.whisper_cli_path)]

        # 添加模型文件
        cmd.extend(['-m', str(self.model_path)])

        # 添加音频文件
        cmd.extend(['-f', audio_path])

        # GPU支持
        if self.use_gpu:
            cmd.append('-ng')  # 使用GPU

        # 语言设置
        language = kwargs.get('language')
        if language:
            cmd.extend(['-l', language])

        # 任务类型
        task = kwargs.get('task', 'transcribe')
        if task == 'translate':
            cmd.append('-tr')

        # 词级时间戳
        if kwargs.get('word_timestamps', True):
            cmd.append('-ml')  # 最大行长度，启用词级时间戳
            cmd.append('1')

        # 温度参数
        temperature = kwargs.get('temperature', 0.0)
        if temperature > 0:
            cmd.extend(['-t', str(temperature)])

        # 线程数
        threads = kwargs.get('n_threads', 0)
        if threads > 0:
            cmd.extend(['-p', str(threads)])

        # 输出格式为JSON
        cmd.append('-oj')

        return cmd

    def _parse_whisper_segments(self, whisper_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析whisper.cpp的输出段"""
        segments = []

        transcription = whisper_result.get("transcription", [])
        if not transcription:
            return segments

        for item in transcription:
            if "timestamps" in item:
                # 处理带时间戳的输出
                timestamps = item["timestamps"]
                text = item.get("text", "")

                segment = {
                    "start": timestamps.get("from", 0) / 1000.0,  # 转换为秒
                    "end": timestamps.get("to", 0) / 1000.0,
                    "text": text.strip()
                }

                # 添加词级时间戳（如果可用）
                if "words" in item:
                    words = []
                    for word_info in item["words"]:
                        word = {
                            "word": word_info.get("word", ""),
                            "start": word_info.get("start", 0) / 1000.0,
                            "end": word_info.get("end", 0) / 1000.0,
                            "probability": word_info.get("probability", 1.0)
                        }
                        words.append(word)
                    segment["words"] = words

                segments.append(segment)

        return segments

    def unload_model(self):
        """卸载模型"""
        if self.model:
            self.module_logger.info("正在卸载whisper.cpp模型...")
            del self.model
            self.model = None
            
            # 清理GPU缓存
            if self.device == "cuda":
                self.device_manager.clear_gpu_cache()
            
            self._update_model_status(ModelStatus.UNLOADED)
            self.module_logger.info("whisper.cpp模型已卸载")

    def _transcribe_with_preprocessing(self, audio_path: str, **kwargs) -> Dict[str, Any]:
        """使用预处理的转录方法"""
        import tempfile
        import soundfile as sf

        start_time = time.time()

        # 执行预处理
        self.module_logger.info("正在执行音频预处理...")
        preprocessing_result = self.preprocessor.process(audio_path)

        if not preprocessing_result.get("success", False):
            self.module_logger.error("音频预处理失败")
            raise RuntimeError("音频预处理失败")

        # 构建转录参数
        transcribe_params = self._build_transcription_params(**kwargs)
        self.module_logger.debug(f"转录参数: {transcribe_params}")

        # 获取预处理后的音频段
        segments = preprocessing_result.get("segments", [])
        if not segments:
            self.module_logger.warning("预处理未产生音频段，回退到直接转录")
            return self._transcribe_without_preprocessing(audio_path, **kwargs)

        self.module_logger.info(f"预处理产生了 {len(segments)} 个音频段")

        # 转录每个音频段
        all_segments = []
        full_text_parts = []

        for i, segment_info in enumerate(segments):
            self.module_logger.debug(f"转录音频段 {i+1}/{len(segments)}")

            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name

                # 保存音频段到临时文件
                sf.write(temp_path, segment_info["audio_data"], segment_info["sample_rate"])

                # 转录音频段
                try:
                    segment_result = self.model.transcribe(temp_path, **transcribe_params)
                except Exception as e:
                    self.module_logger.error(f"段 {segment_info['segment_id']} 转录失败: {e}")
                    # 删除临时文件
                    os.unlink(temp_path)
                    continue

                # 删除临时文件
                os.unlink(temp_path)

                # 处理转录结果
                segment_text = segment_result.get("text", "").strip()
                if segment_text:
                    full_text_parts.append(segment_text)

                    # 调整时间戳
                    segment_start_time = segment_info["start_time"]
                    if "segments" in segment_result:
                        for seg in segment_result["segments"]:
                            # 调整段级时间戳
                            seg["start"] += segment_start_time
                            seg["end"] += segment_start_time

                            # 调整词级时间戳
                            if "words" in seg:
                                for word in seg["words"]:
                                    word["start"] += segment_start_time
                                    word["end"] += segment_start_time

                            all_segments.append(seg)

        # 合并结果
        full_text = " ".join(full_text_parts)
        processing_time = time.time() - start_time

        return {
            "text": full_text,
            "segments": all_segments,
            "language": segments[0].get("language", "unknown") if segments else "unknown",
            "processing_time": processing_time,
            "preprocessing_info": {
                "enabled": True,
                "segments_count": len(segments),
                "preprocessing_time": preprocessing_result.get("processing_time", 0)
            },
            "model_info": {
                "name": "whisper.cpp",
                "backend": "whisper.cpp",
                "device": self.device
            }
        }

    def transcribe_batch(self, audio_files: List[str], **kwargs) -> List[Dict[str, Any]]:
        """批量转录音频文件

        Args:
            audio_files: 音频文件路径列表
            **kwargs: 转录参数

        Returns:
            转录结果列表
        """
        results = []
        total_files = len(audio_files)

        self.module_logger.info(f"开始批量转录 {total_files} 个文件")

        for i, audio_file in enumerate(audio_files, 1):
            self.module_logger.info(f"处理文件 {i}/{total_files}: {audio_file}")

            try:
                result = self.transcribe(audio_file, **kwargs)
                results.append(result)
                self.module_logger.info(f"文件 {i}/{total_files} 转录成功")
            except Exception as e:
                error_result = {
                    "error": str(e),
                    "file": audio_file,
                    "processing_time": 0
                }
                results.append(error_result)
                self.module_logger.error(f"文件 {i}/{total_files} 转录失败: {e}")

        self.module_logger.info(f"批量转录完成，成功 {sum(1 for r in results if 'error' not in r)}/{total_files} 个文件")
        return results

    def __del__(self):
        """析构函数"""
        try:
            self.unload_model()
        except Exception:
            pass
