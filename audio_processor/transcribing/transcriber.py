"""
Whisper 转录器模块
基于 openai/whisper-large-v3-turbo 的音频转录工具
"""

import os
import time
from pathlib import Path
from typing import Dict, Any, List, Optional

import torch
from loguru import logger
from transformers import AutoModelForSpeechSeq2Seq, AutoProcessor
from transformers.pipelines import pipeline

from core.base import BaseProcessor
from utils.monitor import SystemMonitor, ModelMonitorMixin, ModelType, ModelStatus
from utils.device import get_device_manager
from ..preprocessing.processor import AudioPreprocessor


class WhisperTranscriber(BaseProcessor, ModelMonitorMixin):
    """Whisper 音频转录器"""

    def __init__(self, config_name: str = "whisper", enable_preprocessing: Optional[bool] = None):
        """初始化转录器

        Args:
            config_name: 配置名称
            enable_preprocessing: 是否启用预处理（None表示使用配置文件设置）
        """
        super().__init__(config_name)

        # 初始化监控器
        self.monitor = SystemMonitor(self.config)

        # 设置模型监控
        self._setup_model_monitoring(self.monitor, ModelType.WHISPER)
        self._update_model_status(ModelStatus.LOADING)

        # 初始化设备管理器
        self.device_manager = get_device_manager(self.module_logger)
        self.device = self.device_manager.get_preferred_device()
        self.device_manager.log_device_summary()

        # 初始化预处理器
        self.preprocessor: Optional[AudioPreprocessor] = None
        self.enable_preprocessing = enable_preprocessing
        self._init_preprocessor()

        # 初始化模型
        self.model = None
        self.processor = None
        self.pipe = None
        self._load_model()

        # 确保pipeline已初始化
        if self.pipe is None:
            self._update_model_status(ModelStatus.UNLOADED)
            raise RuntimeError("Pipeline初始化失败")

        # 更新模型状态为已加载
        memory_info = self._get_current_memory_info()
        self._update_model_status(ModelStatus.LOADED, memory_info)

    def _init_preprocessor(self):
        """初始化预处理器"""
        try:
            # 如果明确指定了预处理设置，使用指定值
            if self.enable_preprocessing is not None:
                if self.enable_preprocessing:
                    self.preprocessor = AudioPreprocessor("preprocessing")
                    self.module_logger.info("预处理器已启用（通过参数指定）")
                else:
                    self.module_logger.info("预处理器已禁用（通过参数指定）")
            else:
                # 否则根据配置文件决定
                try:
                    preprocessor = AudioPreprocessor("preprocessing")
                    if preprocessor.is_enabled():
                        self.preprocessor = preprocessor
                        self.module_logger.info("预处理器已启用（通过配置文件）")
                    else:
                        self.module_logger.info("预处理器已禁用（通过配置文件）")
                except Exception as e:
                    self.module_logger.warning(f"预处理器初始化失败，将禁用预处理功能: {e}")
                    self.preprocessor = None

        except Exception as e:
            self.module_logger.error(f"预处理器初始化失败: {e}")
            self.preprocessor = None

    def _build_transcription_params(self, **kwargs) -> tuple[Dict[str, Any], Dict[str, Any]]:
        """构建转录参数

        Args:
            **kwargs: 用户传入的参数，会覆盖配置文件中的默认值

        Returns:
            tuple: (generate_kwargs, pipeline_kwargs)
        """
        # 从配置文件获取默认转录参数
        config_params = self.get_config_value("transcription", {})

        # 构建 generate_kwargs (传递给模型的生成参数)
        generate_kwargs = {}

        # 基本参数
        if kwargs.get("language") is not None or config_params.get("language") is not None:
            generate_kwargs["language"] = kwargs.get("language") or config_params.get("language")

        if kwargs.get("task") is not None or config_params.get("task") is not None:
            generate_kwargs["task"] = kwargs.get("task") or config_params.get("task", "transcribe")

        # 初始提示词
        if kwargs.get("initial_prompt") is not None or config_params.get("initial_prompt") is not None:
            generate_kwargs["initial_prompt"] = kwargs.get("initial_prompt") or config_params.get("initial_prompt")

        # 生成参数 - 只包含核心生成参数
        # 处理max_new_tokens - 确保不超过Whisper的限制
        max_new_tokens = kwargs.get("max_new_tokens") or config_params.get("max_new_tokens", 256)
        try:
            max_tokens = int(float(max_new_tokens))
            if max_tokens > 448:  # Whisper模型的token生成上限
                self.module_logger.warning(f"max_new_tokens超出模型限制(448)，自动调整为448: {max_tokens}")
                max_tokens = 448
            generate_kwargs["max_new_tokens"] = max_tokens
        except (ValueError, TypeError):
            self.module_logger.warning(f"无效的max_new_tokens值，使用默认值256: {max_new_tokens}")
            generate_kwargs["max_new_tokens"] = 256

        # 处理num_beams - 确保是整数且不小于1
        num_beams = kwargs.get("num_beams") or config_params.get("num_beams", 1)
        try:
            beam_count = int(float(num_beams))
            if beam_count < 1:
                self.module_logger.warning(f"num_beams必须大于0，自动调整为1: {num_beams}")
                beam_count = 1
            generate_kwargs["num_beams"] = beam_count
        except (ValueError, TypeError):
            self.module_logger.warning(f"无效的num_beams值，使用默认值1: {num_beams}")
            generate_kwargs["num_beams"] = 1
        
        # 温度参数处理
        temperature = kwargs.get("temperature")
        if temperature is None:
            temperature = config_params.get("temperature", 0.0)

        # 检查是否启用温度回退
        temp_fallback = config_params.get("temperature_fallback", {})
        if temp_fallback.get("enable", False) and kwargs.get("temperature") is None:
            # 使用温度回退序列
            generate_kwargs["temperature"] = tuple(temp_fallback.get("temperatures", [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]))
        else:
            generate_kwargs["temperature"] = temperature

        # 构建 pipeline_kwargs (传递给pipeline的参数)
        pipeline_kwargs = {}

        # 时间戳参数 - 直接传递给 pipeline，不放在 generate_kwargs 中
        return_timestamps = kwargs.get("return_timestamps")
        if return_timestamps is None:
            return_timestamps = config_params.get("return_timestamps", True)

        # 只有在启用时间戳的情况下才检查时间戳粒度
        if return_timestamps:
            timestamp_granularity = kwargs.get("timestamp_granularity") or config_params.get("timestamp_granularity")
            if timestamp_granularity == "word":
                pipeline_kwargs["return_timestamps"] = "word"
            else:
                pipeline_kwargs["return_timestamps"] = True
        else:
            pipeline_kwargs["return_timestamps"] = False

        self.module_logger.debug(f"构建的转录参数 - generate_kwargs: {generate_kwargs}")
        self.module_logger.debug(f"构建的转录参数 - pipeline_kwargs: {pipeline_kwargs}")

        return generate_kwargs, pipeline_kwargs

    def _load_model(self):
        """加载Whisper模型"""
        model_name = self.get_config_value("model.name", "openai/whisper-large-v3-turbo")
        self.module_logger.info(f"正在加载模型: {model_name}")

        try:
            # 确定数据类型
            torch_dtype_str = self.get_config_value("model.torch_dtype", "float16")
            torch_dtype = getattr(torch, torch_dtype_str)

            # 加载模型
            self.model = AutoModelForSpeechSeq2Seq.from_pretrained(
                model_name,
                torch_dtype=torch_dtype,
                low_cpu_mem_usage=True,
                use_safetensors=True
            )
            self.model.to(self.device)

            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(model_name)

            # 创建pipeline
            self.pipe = pipeline(
                "automatic-speech-recognition",
                model=self.model,
                tokenizer=self.processor.tokenizer,
                feature_extractor=self.processor.feature_extractor,
                torch_dtype=torch_dtype,
                device=self.device,
            )

            self.module_logger.info("模型加载完成")

        except Exception as e:
            self.module_logger.error(f"模型加载失败: {e}")
            raise

    def process(self, audio_path: str, **kwargs) -> Dict[str, Any]:
        """处理音频文件进行转录

        Args:
            audio_path: 音频文件路径
            **kwargs: 其他参数

        Returns:
            转录结果字典
        """
        return self.transcribe(audio_path, **kwargs)

    def transcribe(self, audio_path: str, **kwargs) -> Dict[str, Any]:
        """转录音频文件

        Args:
            audio_path: 音频文件路径
            **kwargs: 其他参数

        Returns:
            转录结果字典
        """
        self.module_logger.info(f"开始转录音频文件: {audio_path}")

        # 检查文件是否存在
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")

        # 检查文件格式
        file_ext = Path(audio_path).suffix.lower()
        supported_formats = self.get_config_value("audio.supported_formats", [".wav", ".mp3", ".flac", ".m4a", ".ogg"])
        if file_ext not in supported_formats:
            raise ValueError(f"不支持的音频格式: {file_ext}，支持的格式: {supported_formats}")

        # 开始监控
        self.monitor.start_monitoring()

        # 更新模型状态为处理中
        self._update_model_status(ModelStatus.PROCESSING)

        try:
            # 检查是否启用预处理
            if self.preprocessor is not None:
                return self._transcribe_with_preprocessing(audio_path, **kwargs)
            else:
                return self._transcribe_without_preprocessing(audio_path, **kwargs)

        except Exception as e:
            self.module_logger.error(f"转录失败: {e}")
            # 更新模型状态为已加载（处理失败）
            memory_info = self._get_current_memory_info()
            self._update_model_status(ModelStatus.LOADED, memory_info)
            raise
        finally:
            # 停止监控
            self.monitor.stop_monitoring()
            # 恢复模型状态为已加载
            memory_info = self._get_current_memory_info()
            self._update_model_status(ModelStatus.LOADED, memory_info)

    def _transcribe_without_preprocessing(self, audio_path: str, **kwargs) -> Dict[str, Any]:
        """不使用预处理的转录方法"""
        start_time = time.time()

        # 执行转录
        self.module_logger.info("正在执行转录（无预处理）...")

        # 构建转录参数
        generate_kwargs, pipeline_kwargs = self._build_transcription_params(**kwargs)

        self.module_logger.debug(f"转录参数: generate_kwargs={generate_kwargs}, pipeline_kwargs={pipeline_kwargs}")

        # 直接使用官方标准用法
        result = self.pipe(audio_path, generate_kwargs=generate_kwargs, **pipeline_kwargs)  # type: ignore

        end_time = time.time()
        processing_time = end_time - start_time

        # 构建结果
        transcription_result = {
            "text": result["text"],  # type: ignore
            "audio_file": audio_path,
            "processing_time": processing_time,
            "model": self.get_config_value("model.name"),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "preprocessing_enabled": False
        }

        # 添加时间戳信息（如果可用）
        if "chunks" in result:  # type: ignore
            transcription_result["chunks"] = result["chunks"]  # type: ignore

        self.module_logger.info(f"转录完成，耗时: {processing_time:.2f}秒")
        return transcription_result

    def _transcribe_with_preprocessing(self, audio_path: str, **kwargs) -> Dict[str, Any]:
        """使用预处理的转录方法"""
        start_time = time.time()

        self.module_logger.info("正在执行转录（启用预处理）...")

        # 步骤1: 执行预处理
        preprocessing_start = time.time()
        preprocessing_result = self.preprocessor.preprocess_audio(audio_path)
        preprocessing_time = time.time() - preprocessing_start

        self.module_logger.info(f"预处理完成，耗时: {preprocessing_time:.2f}秒，检测到 {len(preprocessing_result.segments)} 个语音段")

        # 步骤2: 获取用于转录的语音段
        transcription_segments = self.preprocessor.get_segments_for_transcription(preprocessing_result)

        if not transcription_segments:
            self.module_logger.warning("未检测到语音段，返回空转录结果")
            return {
                "text": "",
                "audio_file": audio_path,
                "processing_time": time.time() - start_time,
                "preprocessing_time": preprocessing_time,
                "model": self.get_config_value("model.name"),
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "preprocessing_enabled": True,
                "segments": [],
                "preprocessing_stats": preprocessing_result.statistics
            }

        # 步骤3: 转录每个语音段
        transcription_start = time.time()
        segment_results = []

        # 构建转录参数
        generate_kwargs, pipeline_kwargs = self._build_transcription_params(**kwargs)

        self.module_logger.debug(f"预处理转录参数: generate_kwargs={generate_kwargs}, pipeline_kwargs={pipeline_kwargs}")

        for segment_info in transcription_segments:
            try:
                # 使用numpy数组创建临时音频数据进行转录
                import tempfile
                import soundfile as sf

                # 创建临时文件
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                    temp_path = temp_file.name

                    # 保存音频段到临时文件
                    sf.write(temp_path, segment_info["audio_data"], segment_info["sample_rate"])

                    # 转录音频段
                    result = self.pipe(temp_path, generate_kwargs=generate_kwargs, **pipeline_kwargs)  # type: ignore

                    # 删除临时文件
                    os.unlink(temp_path)

                # 构建段转录结果
                segment_result = {
                    "sentence": result["text"],  # type: ignore
                    "translation": "",  # 留空不做处理
                    "startTime": segment_info["start_time"],
                    "endTime": segment_info["end_time"],
                    "segment_id": segment_info["segment_id"]
                }

                segment_results.append(segment_result)

                self.module_logger.debug(f"段 {segment_info['segment_id']} 转录完成: {result['text'][:50]}...")  # type: ignore

            except Exception as e:
                self.module_logger.error(f"段 {segment_info['segment_id']} 转录失败: {e}")
                # 添加错误段
                segment_results.append({
                    "sentence": "",
                    "translation": "",
                    "startTime": segment_info["start_time"],
                    "endTime": segment_info["end_time"],
                    "segment_id": segment_info["segment_id"],
                    "error": str(e)
                })

        transcription_time = time.time() - transcription_start
        total_processing_time = time.time() - start_time

        # 合并所有转录文本
        full_text = " ".join([seg.get("sentence", "") for seg in segment_results if seg.get("sentence")])

        # 构建最终结果
        transcription_result = {
            "text": full_text,
            "audio_file": audio_path,
            "processing_time": total_processing_time,
            "preprocessing_time": preprocessing_time,
            "transcription_time": transcription_time,
            "model": self.get_config_value("model.name"),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "preprocessing_enabled": True,
            "segments": segment_results,
            "preprocessing_stats": preprocessing_result.statistics
        }

        self.module_logger.info(f"预处理转录完成，总耗时: {total_processing_time:.2f}秒（预处理: {preprocessing_time:.2f}s，转录: {transcription_time:.2f}s）")
        return transcription_result

    def transcribe_batch(self, audio_files: List[str], **kwargs) -> List[Dict[str, Any]]:
        """批量转录音频文件

        Args:
            audio_files: 音频文件路径列表
            **kwargs: 其他参数

        Returns:
            转录结果列表
        """
        self.module_logger.info(f"开始批量转录 {len(audio_files)} 个音频文件")

        results = []
        for i, audio_file in enumerate(audio_files, 1):
            self.module_logger.info(f"处理第 {i}/{len(audio_files)} 个文件: {audio_file}")
            try:
                result = self.transcribe(audio_file, **kwargs)
                results.append(result)
            except Exception as e:
                self.module_logger.error(f"文件 {audio_file} 转录失败: {e}")
                results.append({
                    "audio_file": audio_file,
                    "error": str(e),
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                })

        self.module_logger.info(f"批量转录完成，成功: {len([r for r in results if 'error' not in r])}/{len(audio_files)}")
        return results
