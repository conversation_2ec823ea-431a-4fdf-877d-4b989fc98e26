"""
原始OpenAI Whisper转录器模块
基于原始的OpenAI Whisper库进行音频转录
"""

import os
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Union

import torch
import whisper
from loguru import logger

from core.base import BaseProcessor
from utils.monitor import SystemMonitor, ModelMonitorMixin, ModelType, ModelStatus
from utils.device import get_device_manager
from ..preprocessing.processor import AudioPreprocessor


class OriginalWhisperTranscriber(BaseProcessor, ModelMonitorMixin):
    """原始OpenAI Whisper音频转录器"""

    def __init__(self, config_name: str = "whisper", enable_preprocessing: Optional[bool] = None):
        """初始化转录器

        Args:
            config_name: 配置名称
            enable_preprocessing: 是否启用预处理（None表示使用配置文件设置）
        """
        super().__init__(config_name)

        # 初始化监控器
        self.monitor = SystemMonitor(self.config)

        # 设置模型监控
        self._setup_model_monitoring(self.monitor, ModelType.WHISPER)
        self._update_model_status(ModelStatus.LOADING)

        # 初始化设备管理器
        self.device_manager = get_device_manager(self.module_logger)
        self.device = self.device_manager.get_preferred_device()
        self.device_manager.log_device_summary()

        # 初始化预处理器
        self.preprocessor: Optional[AudioPreprocessor] = None
        self.enable_preprocessing = enable_preprocessing
        self._init_preprocessor()

        # 初始化模型
        self.model = None
        self._load_model()

        # 确保模型已初始化
        if self.model is None:
            self._update_model_status(ModelStatus.UNLOADED)
            raise RuntimeError("Whisper模型初始化失败")

        # 更新模型状态为已加载
        memory_info = self._get_current_memory_info()
        self._update_model_status(ModelStatus.LOADED, memory_info)

    def _init_preprocessor(self):
        """初始化预处理器"""
        try:
            # 如果明确指定了预处理设置，使用指定值
            if self.enable_preprocessing is not None:
                if self.enable_preprocessing:
                    self.preprocessor = AudioPreprocessor("preprocessing")
                    self.module_logger.info("预处理器已启用（通过参数指定）")
                else:
                    self.module_logger.info("预处理器已禁用（通过参数指定）")
            else:
                # 否则根据配置文件决定
                try:
                    preprocessor = AudioPreprocessor("preprocessing")
                    if preprocessor.is_enabled():
                        self.preprocessor = preprocessor
                        self.module_logger.info("预处理器已启用（通过配置文件）")
                    else:
                        self.module_logger.info("预处理器已禁用（通过配置文件）")
                except Exception as e:
                    self.module_logger.warning(f"预处理器初始化失败，将禁用预处理功能: {e}")
                    self.preprocessor = None

        except Exception as e:
            self.module_logger.error(f"预处理器初始化失败: {e}")
            self.preprocessor = None

    def _build_transcription_params(self, **kwargs) -> Dict[str, Any]:
        """构建转录参数

        Args:
            **kwargs: 用户传入的参数，会覆盖配置文件中的默认值

        Returns:
            Dict: 转录参数字典
        """
        # 从配置文件获取默认转录参数
        config_params = self.get_config_value("transcription", {})

        # 构建转录参数
        transcribe_params = {}

        # 基本参数
        if kwargs.get("language") is not None or config_params.get("language") is not None:
            transcribe_params["language"] = kwargs.get("language") or config_params.get("language")

        if kwargs.get("task") is not None or config_params.get("task") is not None:
            transcribe_params["task"] = kwargs.get("task") or config_params.get("task", "transcribe")

        # 初始提示词
        if kwargs.get("initial_prompt") is not None or config_params.get("initial_prompt") is not None:
            transcribe_params["initial_prompt"] = kwargs.get("initial_prompt") or config_params.get("initial_prompt")

        # 词级别时间戳
        word_timestamps = kwargs.get("word_timestamps")
        if word_timestamps is None:
            # 检查timestamp_granularity参数（兼容性）
            timestamp_granularity = kwargs.get("timestamp_granularity") or config_params.get("timestamp_granularity", "segment")
            if timestamp_granularity == "word":
                word_timestamps = True
            else:
                word_timestamps = config_params.get("word_timestamps", True)
        transcribe_params["word_timestamps"] = word_timestamps

        # 温度参数处理
        temperature = kwargs.get("temperature")
        if temperature is None:
            temperature = config_params.get("temperature", 0.0)
        transcribe_params["temperature"] = temperature

        # 上下文条件生成
        condition_on_previous_text = kwargs.get("condition_on_previous_text")
        if condition_on_previous_text is None:
            # 兼容旧参数名
            condition_on_previous_text = kwargs.get("condition_on_prev_tokens")
            if condition_on_previous_text is None:
                condition_on_previous_text = config_params.get("condition_on_previous_text", True)
        transcribe_params["condition_on_previous_text"] = condition_on_previous_text

        # 注意：carry_initial_prompt 参数在原始 OpenAI Whisper 中不支持，已移除

        # 质量控制参数
        compression_ratio_threshold = kwargs.get("compression_ratio_threshold") or config_params.get("compression_ratio_threshold")
        if compression_ratio_threshold is not None:
            transcribe_params["compression_ratio_threshold"] = compression_ratio_threshold

        logprob_threshold = kwargs.get("logprob_threshold") or config_params.get("logprob_threshold")
        if logprob_threshold is not None:
            transcribe_params["logprob_threshold"] = logprob_threshold

        no_speech_threshold = kwargs.get("no_speech_threshold") or config_params.get("no_speech_threshold")
        if no_speech_threshold is not None:
            transcribe_params["no_speech_threshold"] = no_speech_threshold

        # 标点符号参数
        prepend_punctuations = kwargs.get("prepend_punctuations") or config_params.get("prepend_punctuations")
        if prepend_punctuations is not None:
            transcribe_params["prepend_punctuations"] = prepend_punctuations

        append_punctuations = kwargs.get("append_punctuations") or config_params.get("append_punctuations")
        if append_punctuations is not None:
            transcribe_params["append_punctuations"] = append_punctuations

        # 时间戳裁剪
        clip_timestamps = kwargs.get("clip_timestamps") or config_params.get("clip_timestamps")
        if clip_timestamps is not None:
            transcribe_params["clip_timestamps"] = clip_timestamps

        # 幻觉静音阈值
        hallucination_silence_threshold = kwargs.get("hallucination_silence_threshold") or config_params.get("hallucination_silence_threshold")
        if hallucination_silence_threshold is not None:
            transcribe_params["hallucination_silence_threshold"] = hallucination_silence_threshold

        # 设置详细输出
        verbose = kwargs.get("verbose")
        if verbose is None:
            verbose = config_params.get("verbose", True)
        transcribe_params["verbose"] = verbose

        self.module_logger.debug(f"构建的转录参数: {transcribe_params}")
        return transcribe_params

    def _load_model(self):
        """加载原始OpenAI Whisper模型"""
        model_config = self.get_config_value("model", {})
        model_name = model_config.get("name", "large-v3").lower()
            
        # 保存模型大小信息
        self.model_size = model_name

        self.module_logger.info(f"正在加载原始OpenAI Whisper模型: {model_name}")

        try:
            # 加载模型
            self.model = whisper.load_model(model_name)
            self.module_logger.info("原始OpenAI Whisper模型加载完成")
        except Exception as e:
            self.module_logger.error(f"Whisper模型加载失败: {e}")
            raise

    def process(self, audio_path: str, **kwargs) -> Dict[str, Any]:
        """处理音频文件进行转录

        Args:
            audio_path: 音频文件路径
            **kwargs: 其他参数

        Returns:
            转录结果字典
        """
        return self.transcribe(audio_path, **kwargs)

    def transcribe(self, audio_path: str, **kwargs) -> Dict[str, Any]:
        """转录音频文件

        Args:
            audio_path: 音频文件路径
            **kwargs: 其他参数

        Returns:
            转录结果字典
        """
        self.module_logger.info(f"开始转录音频文件: {audio_path}")

        # 检查文件是否存在
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")

        # 检查文件格式
        file_ext = Path(audio_path).suffix.lower()
        supported_formats = self.get_config_value("audio.supported_formats", [".wav", ".mp3", ".flac", ".m4a", ".ogg"])
        if file_ext not in supported_formats:
            raise ValueError(f"不支持的音频格式: {file_ext}，支持的格式: {supported_formats}")

        # 开始监控
        self.monitor.start_monitoring()

        # 更新模型状态为处理中
        self._update_model_status(ModelStatus.PROCESSING)

        try:
            # 检查是否启用预处理
            if self.preprocessor is not None:
                return self._transcribe_with_preprocessing(audio_path, **kwargs)
            else:
                return self._transcribe_without_preprocessing(audio_path, **kwargs)

        except Exception as e:
            self.module_logger.error(f"转录失败: {e}")
            # 更新模型状态为已加载（处理失败）
            memory_info = self._get_current_memory_info()
            self._update_model_status(ModelStatus.LOADED, memory_info)
            raise
        finally:
            # 停止监控
            self.monitor.stop_monitoring()
            # 恢复模型状态为已加载
            memory_info = self._get_current_memory_info()
            self._update_model_status(ModelStatus.LOADED, memory_info)

    def _transcribe_without_preprocessing(self, audio_path: str, **kwargs) -> Dict[str, Any]:
        """不使用预处理的转录方法"""
        start_time = time.time()

        # 执行转录
        self.module_logger.info("正在执行转录（无预处理）...")

        # 构建转录参数
        transcribe_params = self._build_transcription_params(**kwargs)
        self.module_logger.debug(f"转录参数: {transcribe_params}")

        # 使用原始Whisper转录方法
        try:
            result = self.model.transcribe(audio_path, **transcribe_params)
        except AttributeError as e:
            if "Cannot set attribute 'src' directly" in str(e):
                # Triton兼容性问题，禁用词级别时间戳重试
                self.module_logger.warning("检测到Triton兼容性问题，禁用词级别时间戳重试")
                transcribe_params_fallback = transcribe_params.copy()
                transcribe_params_fallback["word_timestamps"] = False
                result = self.model.transcribe(audio_path, **transcribe_params_fallback)
            else:
                raise

        end_time = time.time()
        processing_time = end_time - start_time

        # 构建与项目格式兼容的结果
        transcription_result = self._convert_to_project_format(result, audio_path, processing_time)

        self.module_logger.info(f"转录完成，耗时: {processing_time:.2f}秒")
        return transcription_result

    def _transcribe_with_preprocessing(self, audio_path: str, **kwargs) -> Dict[str, Any]:
        """使用预处理的转录方法"""
        start_time = time.time()

        self.module_logger.info("正在执行转录（启用预处理）...")

        # 步骤1: 执行预处理
        preprocessing_start = time.time()
        preprocessing_result = self.preprocessor.preprocess_audio(audio_path)
        preprocessing_time = time.time() - preprocessing_start

        self.module_logger.info(f"预处理完成，耗时: {preprocessing_time:.2f}秒，检测到 {len(preprocessing_result.segments)} 个语音段")

        # 步骤2: 获取用于转录的语音段
        transcription_segments = self.preprocessor.get_segments_for_transcription(preprocessing_result)

        if not transcription_segments:
            self.module_logger.warning("未检测到语音段，返回空转录结果")
            return {
                "text": "",
                "audio_file": audio_path,
                "processing_time": time.time() - start_time,
                "preprocessing_time": preprocessing_time,
                "model": f"original-whisper-{self.model_size}",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "preprocessing_enabled": True,
                "segments": [],
                "preprocessing_stats": preprocessing_result.statistics
            }

        # 步骤3: 转录每个语音段
        transcription_start = time.time()
        segment_results = []

        # 构建转录参数
        transcribe_params = self._build_transcription_params(**kwargs)
        self.module_logger.debug(f"预处理转录参数: {transcribe_params}")

        for segment_info in transcription_segments:
            try:
                # 使用numpy数组创建临时音频数据进行转录
                import tempfile
                import soundfile as sf

                # 创建临时文件
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                    temp_path = temp_file.name

                    # 保存音频段到临时文件
                    sf.write(temp_path, segment_info["audio_data"], segment_info["sample_rate"])

                    # 转录音频段
                    try:
                        segment_result = self.model.transcribe(temp_path, **transcribe_params)
                    except AttributeError as e:
                        if "Cannot set attribute 'src' directly" in str(e):
                            # Triton兼容性问题，禁用词级别时间戳重试
                            self.module_logger.warning(f"段 {segment_info['segment_id']} 检测到Triton兼容性问题，禁用词级别时间戳重试")
                            transcribe_params_fallback = transcribe_params.copy()
                            transcribe_params_fallback["word_timestamps"] = False
                            segment_result = self.model.transcribe(temp_path, **transcribe_params_fallback)
                        else:
                            raise

                    # 删除临时文件
                    os.unlink(temp_path)

                # 构建段转录结果
                segment_info = {
                    "sentence": segment_result["text"],
                    "translation": "",  # 留空不做处理
                    "startTime": segment_info["start_time"],
                    "endTime": segment_info["end_time"],
                    "segment_id": segment_info["segment_id"]
                }

                segment_results.append(segment_info)

                self.module_logger.debug(f"段 {segment_info['segment_id']} 转录完成: {segment_result['text'][:50]}...")

            except Exception as e:
                self.module_logger.error(f"段 {segment_info['segment_id']} 转录失败: {e}")
                # 添加错误段
                segment_results.append({
                    "sentence": "",
                    "translation": "",
                    "startTime": segment_info["start_time"],
                    "endTime": segment_info["end_time"],
                    "segment_id": segment_info["segment_id"],
                    "error": str(e)
                })

        transcription_time = time.time() - transcription_start
        total_processing_time = time.time() - start_time

        # 合并所有转录文本
        full_text = " ".join([seg.get("sentence", "") for seg in segment_results if seg.get("sentence")])

        # 构建最终结果
        transcription_result = {
            "text": full_text,
            "audio_file": audio_path,
            "processing_time": total_processing_time,
            "preprocessing_time": preprocessing_time,
            "transcription_time": transcription_time,
            "model": f"original-whisper-{self.model_size}",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "preprocessing_enabled": True,
            "segments": segment_results,
            "preprocessing_stats": preprocessing_result.statistics
        }

        self.module_logger.info(f"预处理转录完成，总耗时: {total_processing_time:.2f}秒（预处理: {preprocessing_time:.2f}s，转录: {transcription_time:.2f}s）")
        return transcription_result

    def _convert_to_project_format(self, whisper_result: Dict[str, Any], audio_path: str, processing_time: float) -> Dict[str, Any]:
        """将原始Whisper结果转换为项目格式
        
        Args:
            whisper_result: 原始Whisper转录结果
            audio_path: 音频文件路径
            processing_time: 处理时间
            
        Returns:
            转换后的结果
        """
        # 构建基本结果
        project_result = {
            "text": whisper_result["text"],
            "audio_file": audio_path,
            "processing_time": processing_time,
            "model": f"original-whisper-{self.model_size}",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "preprocessing_enabled": False,
            "original_result": whisper_result  # 保留原始结果以供参考
        }
        
        # 添加分段信息
        chunks = []
        for segment in whisper_result["segments"]:
            chunk = {
                "text": segment["text"],
                "timestamp": [segment["start"], segment["end"]]
            }
            
            # 添加词级别时间戳（如果可用）
            if "words" in segment:
                chunk["words"] = []
                for word in segment["words"]:
                    chunk["words"].append({
                        "text": word["word"],
                        "timestamp": [word["start"], word["end"]]
                    })
            
            chunks.append(chunk)
        
        project_result["chunks"] = chunks
        
        return project_result

    def transcribe_batch(self, audio_files: List[str], **kwargs) -> List[Dict[str, Any]]:
        """批量转录音频文件

        Args:
            audio_files: 音频文件路径列表
            **kwargs: 其他参数

        Returns:
            转录结果列表
        """
        self.module_logger.info(f"开始批量转录 {len(audio_files)} 个音频文件")

        results = []
        for i, audio_file in enumerate(audio_files, 1):
            self.module_logger.info(f"处理第 {i}/{len(audio_files)} 个文件: {audio_file}")
            try:
                result = self.transcribe(audio_file, **kwargs)
                results.append(result)
            except Exception as e:
                self.module_logger.error(f"文件 {audio_file} 转录失败: {e}")
                results.append({
                    "audio_file": audio_file,
                    "error": str(e),
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                })

        self.module_logger.info(f"批量转录完成，成功: {len([r for r in results if 'error' not in r])}/{len(audio_files)}")
        return results 