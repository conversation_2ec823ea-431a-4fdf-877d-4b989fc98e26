"""
音频预处理主处理器
整合音频标准化、降噪、VAD检测和语音段合并功能
"""

import numpy as np
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger

from core.base import BaseProcessor
from utils.monitor import SystemMonitor, ModelMonitorMixin, ModelType, ModelStatus
from utils.device import get_device_manager

from .audio_normalizer import AudioNormalizer
from .window_processor import WindowProcessor, AudioWindow
from .denoiser import AudioDenoiser, SimpleDenoiser
from .vad_detector import VADDetector, SpeechSegment
from .segment_merger import SegmentMerger, MergedSegment


class PreprocessingResult:
    """预处理结果数据结构"""

    def __init__(self,
                 segments: List[MergedSegment],
                 audio_data: np.ndarray,
                 sample_rate: int,
                 processing_time: float,
                 statistics: Dict[str, Any]):
        """初始化预处理结果

        Args:
            segments: 合并后的语音段列表
            audio_data: 预处理后的音频数据
            sample_rate: 采样率
            processing_time: 处理时间
            statistics: 处理统计信息
        """
        self.segments = segments
        self.audio_data = audio_data
        self.sample_rate = sample_rate
        self.processing_time = processing_time
        self.statistics = statistics

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "segments": [seg.to_dict() for seg in self.segments],
            "audio_info": {
                "sample_rate": self.sample_rate,
                "duration": len(self.audio_data) / self.sample_rate,
                "samples": len(self.audio_data)
            },
            "processing_time": self.processing_time,
            "statistics": self.statistics
        }


class AudioPreprocessor(BaseProcessor, ModelMonitorMixin):
    """音频预处理器"""

    def __init__(self, config_name: str = "preprocessing"):
        """初始化音频预处理器

        Args:
            config_name: 配置名称
        """
        super().__init__(config_name)

        # 初始化监控器
        self.monitor = SystemMonitor(self.config)
        self._setup_model_monitoring(self.monitor, ModelType.WHISPER)  # 使用WHISPER类型作为预处理
        self._update_model_status(ModelStatus.LOADING)

        # 初始化设备管理器
        self.device_manager = get_device_manager(self.module_logger)
        self.device = self.device_manager.get_preferred_device()

        # 检查是否启用预处理
        self.enabled = self.get_config_value("enable", False)

        if not self.enabled:
            self.module_logger.info("音频预处理功能已禁用")
            self._update_model_status(ModelStatus.UNLOADED)
            return

        # 初始化各个组件
        self._init_components()

        self._update_model_status(ModelStatus.LOADED)
        self.module_logger.info("音频预处理器初始化完成")

    def _init_components(self):
        """初始化预处理组件"""
        try:
            # 1. 音频标准化器
            audio_config = self.get_config_value("audio", {})
            self.normalizer = AudioNormalizer(
                target_sample_rate=audio_config.get("target_sample_rate", 16000),
                target_channels=audio_config.get("target_channels", 1),
                normalize_audio=audio_config.get("normalize_audio", True),
                normalization_method=audio_config.get("normalization_method", "peak")
            )

            # 2. 滑动窗口处理器
            windowing_config = self.get_config_value("windowing", {})
            self.window_processor = WindowProcessor(
                window_duration=windowing_config.get("window_duration", 10.0),
                hop_duration=windowing_config.get("hop_duration", 5.0)
            )

            # 3. 降噪器
            denoising_config = self.get_config_value("denoising", {})
            denoising_enabled = denoising_config.get("enable", True)

            if denoising_enabled:
                try:
                    self.denoiser = AudioDenoiser(
                        model_name=denoising_config.get("model_name", "DeepFilterNet3"),
                        device=denoising_config.get("device", self.device),
                        enable=True
                    )
                except Exception as e:
                    self.module_logger.warning(f"DeepFilterNet加载失败，使用简化降噪器: {e}")
                    self.denoiser = SimpleDenoiser()
            else:
                self.denoiser = None
                self.module_logger.info("降噪功能已禁用")

            # 4. VAD检测器
            vad_config = self.get_config_value("vad", {})
            self.vad_detector = VADDetector(
                model_name=vad_config.get("model_name", "silero_vad"),
                threshold=vad_config.get("threshold", 0.5),
                min_speech_duration=vad_config.get("min_speech_duration", 0.25),
                min_silence_duration=vad_config.get("min_silence_duration", 0.1),
                window_size_samples=vad_config.get("window_size_samples", 1536),
                device=self.device
            )

            # 5. 语音段合并器
            merging_config = self.get_config_value("segment_merging", {})
            self.segment_merger = SegmentMerger(
                overlap_threshold=merging_config.get("overlap_threshold", 0.5),
                min_segment_duration=merging_config.get("min_segment_duration", 0.5),
                max_segment_duration=merging_config.get("max_segment_duration", 30.0)
            )

        except Exception as e:
            self.module_logger.error(f"初始化预处理组件失败: {e}")
            raise

    def process(self, audio_path: str, **kwargs) -> PreprocessingResult:
        """处理音频文件进行预处理

        Args:
            audio_path: 音频文件路径
            **kwargs: 其他参数

        Returns:
            预处理结果
        """
        return self.preprocess_audio(audio_path, **kwargs)

    def preprocess_audio(self, audio_path: str, **kwargs) -> PreprocessingResult:
        """预处理音频文件

        Args:
            audio_path: 音频文件路径
            **kwargs: 其他参数

        Returns:
            预处理结果
        """
        if not self.enabled:
            raise RuntimeError("音频预处理功能未启用")

        self.module_logger.info(f"开始预处理音频文件: {audio_path}")
        self._update_model_status(ModelStatus.PROCESSING)

        start_time = time.time()

        try:
            # 步骤1: 音频标准化
            self.module_logger.info("步骤1: 音频标准化")
            normalized_audio, sample_rate = self.normalizer.normalize_audio_file(audio_path)

            # 步骤2: 创建滑动窗口
            self.module_logger.info("步骤2: 创建滑动窗口")
            windows = self.window_processor.create_windows(normalized_audio, sample_rate)

            # 步骤3-4: 处理每个窗口（降噪 + VAD检测）
            self.module_logger.info("步骤3-4: 窗口处理（降噪 + VAD检测）")
            all_segments = []

            for window in windows:
                # 降噪处理
                if self.denoiser is not None:
                    denoised_audio = self.denoiser.denoise_audio(window.audio_data, sample_rate)
                else:
                    denoised_audio = window.audio_data

                # VAD检测
                segments = self.vad_detector.detect_speech_segments(
                    denoised_audio,
                    sample_rate,
                    window.start_time
                )
                all_segments.extend(segments)

            # 步骤5: 合并重叠语音段
            self.module_logger.info("步骤5: 合并重叠语音段")
            merged_segments = self.segment_merger.merge_segments(all_segments)

            # 计算处理时间
            processing_time = time.time() - start_time

            # 生成统计信息
            statistics = self._generate_statistics(
                audio_path, normalized_audio, sample_rate,
                windows, all_segments, merged_segments, processing_time
            )

            # 创建结果对象
            result = PreprocessingResult(
                segments=merged_segments,
                audio_data=normalized_audio,
                sample_rate=sample_rate,
                processing_time=processing_time,
                statistics=statistics
            )

            self.module_logger.info(f"音频预处理完成，耗时: {processing_time:.2f}s，检测到 {len(merged_segments)} 个语音段")
            self._update_model_status(ModelStatus.LOADED)

            return result

        except Exception as e:
            self.module_logger.error(f"音频预处理失败: {e}")
            self._update_model_status(ModelStatus.LOADED)
            raise

    def _generate_statistics(self,
                           audio_path: str,
                           audio_data: np.ndarray,
                           sample_rate: int,
                           windows: List[AudioWindow],
                           original_segments: List[SpeechSegment],
                           merged_segments: List[MergedSegment],
                           processing_time: float) -> Dict[str, Any]:
        """生成处理统计信息

        Args:
            audio_path: 音频文件路径
            audio_data: 音频数据
            sample_rate: 采样率
            windows: 窗口列表
            original_segments: 原始语音段
            merged_segments: 合并后语音段
            processing_time: 处理时间

        Returns:
            统计信息字典
        """
        total_duration = len(audio_data) / sample_rate

        # 窗口统计
        window_stats = self.window_processor.get_window_info(total_duration)

        # 语音段统计
        segment_stats = self.segment_merger.get_merge_statistics(original_segments, merged_segments)

        # 语音占比统计
        total_speech_duration = sum(seg.duration for seg in merged_segments)
        speech_ratio = total_speech_duration / total_duration if total_duration > 0 else 0

        return {
            "audio_file": audio_path,
            "audio_duration": total_duration,
            "sample_rate": sample_rate,
            "processing_time": processing_time,
            "processing_speed": total_duration / processing_time if processing_time > 0 else 0,
            "window_stats": window_stats,
            "segment_stats": segment_stats,
            "speech_ratio": speech_ratio,
            "total_speech_duration": total_speech_duration,
            "components": {
                "normalizer": True,
                "denoiser": self.denoiser is not None,
                "vad_detector": True,
                "segment_merger": True
            }
        }

    def extract_audio_segment(self,
                            audio_data: np.ndarray,
                            sample_rate: int,
                            start_time: float,
                            end_time: float) -> np.ndarray:
        """提取音频段

        Args:
            audio_data: 音频数据
            sample_rate: 采样率
            start_time: 开始时间（秒）
            end_time: 结束时间（秒）

        Returns:
            音频段数据
        """
        start_sample = int(start_time * sample_rate)
        end_sample = int(end_time * sample_rate)

        # 确保索引在有效范围内
        start_sample = max(0, start_sample)
        end_sample = min(len(audio_data), end_sample)

        return audio_data[start_sample:end_sample]

    def get_segments_for_transcription(self, result: PreprocessingResult) -> List[Dict[str, Any]]:
        """获取用于转录的语音段信息

        Args:
            result: 预处理结果

        Returns:
            转录段信息列表
        """
        transcription_segments = []

        for segment in result.segments:
            # 提取音频段
            audio_segment = self.extract_audio_segment(
                result.audio_data,
                result.sample_rate,
                segment.start,
                segment.end
            )

            segment_info = {
                "segment_id": segment.segment_id,
                "start_time": segment.start,
                "end_time": segment.end,
                "duration": segment.duration,
                "audio_data": audio_segment,
                "sample_rate": result.sample_rate,
                "confidence": segment.confidence
            }

            transcription_segments.append(segment_info)

        return transcription_segments

    def is_enabled(self) -> bool:
        """检查预处理是否启用

        Returns:
            是否启用
        """
        return self.enabled

    def get_component_info(self) -> Dict[str, Any]:
        """获取组件信息

        Returns:
            组件信息字典
        """
        info = {
            "enabled": self.enabled,
            "device": self.device
        }

        if self.enabled:
            info.update({
                "normalizer": {
                    "target_sample_rate": self.normalizer.target_sample_rate,
                    "target_channels": self.normalizer.target_channels,
                    "normalize_audio": self.normalizer.normalize_audio,
                    "normalization_method": self.normalizer.normalization_method
                },
                "window_processor": {
                    "window_duration": self.window_processor.window_duration,
                    "hop_duration": self.window_processor.hop_duration,
                    "overlap_duration": self.window_processor.overlap_duration
                },
                "denoiser": self.denoiser.get_model_info() if (self.denoiser and hasattr(self.denoiser, 'get_model_info')) else {"type": "simple"},
                "vad_detector": self.vad_detector.get_model_info(),
                "segment_merger": {
                    "overlap_threshold": self.segment_merger.overlap_threshold,
                    "min_segment_duration": self.segment_merger.min_segment_duration,
                    "max_segment_duration": self.segment_merger.max_segment_duration
                }
            })

        return info

    def unload_models(self):
        """卸载所有模型释放内存"""
        if not self.enabled:
            return

        try:
            if self.denoiser and hasattr(self.denoiser, 'unload_model'):
                self.denoiser.unload_model()

            if self.vad_detector and hasattr(self.vad_detector, 'unload_model'):
                self.vad_detector.unload_model()

            self._update_model_status(ModelStatus.UNLOADED)
            self.module_logger.info("预处理模型已卸载")

        except Exception as e:
            self.module_logger.error(f"卸载预处理模型失败: {e}")

    def clear_cache(self):
        """清理缓存"""
        if self.enabled and hasattr(self.monitor, 'clear_gpu_cache'):
            self.monitor.clear_gpu_cache()
            self.module_logger.info("预处理器缓存已清理")
