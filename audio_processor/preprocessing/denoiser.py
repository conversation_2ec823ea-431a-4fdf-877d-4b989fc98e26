"""
DeepFilterNet降噪模块
使用深度学习进行音频降噪处理
"""

import numpy as np
import torch
from typing import Optional, Union
from loguru import logger

try:
    from df.enhance import enhance, init_df
    DEEPFILTERNET_AVAILABLE = True
except ImportError:
    DEEPFILTERNET_AVAILABLE = False
    logger.warning("DeepFilterNet 不可用，降噪功能将被禁用")


class AudioDenoiser:
    """音频降噪处理器"""

    def __init__(self,
                 model_name: str = "DeepFilterNet3",
                 device: str = "cuda",
                 enable: bool = True):
        """初始化音频降噪器

        Args:
            model_name: 降噪模型名称
            device: 计算设备
            enable: 是否启用降噪
        """
        self.model_name = model_name
        self.device = device
        self.enable = enable and DEEPFILTERNET_AVAILABLE
        self.model = None
        self.df_state = None

        if self.enable:
            self._load_model()
        else:
            logger.warning("音频降噪功能已禁用")

    def _load_model(self):
        """加载DeepFilterNet模型"""
        try:
            logger.info(f"正在加载 {self.model_name} 降噪模型...")

            # 初始化DeepFilterNet
            self.model, self.df_state, _ = init_df(
                model_base_dir=None,  # 使用默认模型路径
                config_allow_defaults=True
            )

            # 设置设备
            if self.device == "cuda" and torch.cuda.is_available():
                self.model = self.model.to("cuda")
                logger.info("降噪模型已加载到GPU")
            else:
                self.model = self.model.to("cpu")
                logger.info("降噪模型已加载到CPU")

            logger.info(f"{self.model_name} 降噪模型加载完成")

        except Exception as e:
            logger.error(f"加载降噪模型失败: {e}")
            self.enable = False
            raise

    def denoise_audio(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """对音频进行降噪处理

        Args:
            audio_data: 输入音频数据
            sample_rate: 采样率

        Returns:
            降噪后的音频数据
        """
        if not self.enable:
            logger.debug("降噪功能已禁用，返回原始音频")
            return audio_data

        try:
            logger.debug(f"开始降噪处理 - 音频长度: {len(audio_data)} 样本, 采样率: {sample_rate}Hz")

            # 确保音频是单声道
            if len(audio_data.shape) > 1:
                audio_data = np.mean(audio_data, axis=0)

            # 确保音频数据类型为float32
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)

            # DeepFilterNet需要特定的采样率（通常是48kHz）
            target_sr = self.df_state.sr()
            if sample_rate != target_sr:
                # 使用librosa进行重采样（避免torch/numpy兼容性问题）
                import librosa
                audio_resampled = librosa.resample(audio_data, orig_sr=sample_rate, target_sr=target_sr)
                logger.debug(f"音频已重采样从 {sample_rate}Hz 到 {target_sr}Hz")
            else:
                audio_resampled = audio_data

            # 执行降噪
            # 检查模型和状态是否已加载
            if self.model is None or self.df_state is None:
                logger.error("降噪模型或状态未加载")
                return audio_data

            with torch.no_grad():
                # 确保音频数据是torch tensor并在正确的设备上
                if isinstance(audio_resampled, np.ndarray):
                    audio_tensor = torch.from_numpy(audio_resampled).float()
                else:
                    audio_tensor = audio_resampled.float()

                # 重要：DeepFilterNet内部有bug，需要确保tensor在CPU上
                # 即使模型在GPU上，输入tensor也必须在CPU上以避免内部的.numpy()调用错误
                # 不要移动到GPU设备，保持在CPU上
                # audio_tensor = audio_tensor.to(self.device)  # 注释掉这行

                # DeepFilterNet期望的形状是 [C, T] (channels, time)
                # 如果是1D音频，添加通道维度
                if audio_tensor.dim() == 1:
                    audio_tensor = audio_tensor.unsqueeze(0)  # (1, samples)
                elif audio_tensor.dim() == 2 and audio_tensor.shape[0] > audio_tensor.shape[1]:
                    # 如果是 (samples, channels) 格式，转置为 (channels, samples)
                    audio_tensor = audio_tensor.transpose(0, 1)

                # 确保tensor是连续的（修复cuDNN错误）
                audio_tensor = audio_tensor.contiguous()

                logger.debug(f"降噪输入音频形状: {audio_tensor.shape}, 设备: {audio_tensor.device}, 连续性: {audio_tensor.is_contiguous()}")

                # 尝试不同的cuDNN设置来避免兼容性问题
                original_benchmark = torch.backends.cudnn.benchmark
                original_deterministic = torch.backends.cudnn.deterministic

                try:
                    # 设置cuDNN为确定性模式，可能避免某些兼容性问题
                    torch.backends.cudnn.benchmark = False
                    torch.backends.cudnn.deterministic = True

                    # 调用DeepFilterNet的enhance函数
                    enhanced_audio = enhance(
                        model=self.model,
                        df_state=self.df_state,
                        audio=audio_tensor,
                        pad=True  # 补偿STFT/ISTFT延迟
                    )

                except RuntimeError as e:
                    if "cuDNN" in str(e):
                        logger.warning(f"cuDNN错误，尝试禁用cuDNN: {e}")
                        # 临时禁用cuDNN重试
                        original_enabled = torch.backends.cudnn.enabled
                        try:
                            torch.backends.cudnn.enabled = False
                            enhanced_audio = enhance(
                                model=self.model,
                                df_state=self.df_state,
                                audio=audio_tensor,
                                pad=True
                            )
                            logger.info("禁用cuDNN后降噪成功")
                        finally:
                            torch.backends.cudnn.enabled = original_enabled
                    else:
                        raise
                finally:
                    # 恢复原始cuDNN设置
                    torch.backends.cudnn.benchmark = original_benchmark
                    torch.backends.cudnn.deterministic = original_deterministic

                logger.debug(f"降噪输出音频形状: {enhanced_audio.shape}")
                logger.debug(f"降噪输出音频类型: {type(enhanced_audio)}")
                logger.debug(f"降噪输出音频设备: {enhanced_audio.device if hasattr(enhanced_audio, 'device') else 'N/A'}")

                # 转换回numpy数组，移除通道维度（如果只有一个通道）
                if isinstance(enhanced_audio, torch.Tensor):
                    # 确保tensor在CPU上再转换为numpy
                    logger.debug("将tensor转换为numpy数组")
                    enhanced_audio = enhanced_audio.detach().cpu().numpy()
                    logger.debug(f"转换后形状: {enhanced_audio.shape}, 类型: {type(enhanced_audio)}")
                    if enhanced_audio.shape[0] == 1:  # 单通道
                        enhanced_audio = enhanced_audio.squeeze(0)  # 移除通道维度
                        logger.debug(f"移除通道维度后形状: {enhanced_audio.shape}")

            # 如果需要，重采样回原始采样率
            if sample_rate != target_sr:
                # 确保enhanced_audio是numpy数组
                if isinstance(enhanced_audio, torch.Tensor):
                    enhanced_audio = enhanced_audio.cpu().numpy()

                # 使用librosa进行重采样
                import librosa
                enhanced_audio = librosa.resample(enhanced_audio, orig_sr=target_sr, target_sr=sample_rate)
                logger.debug(f"降噪音频已重采样回 {sample_rate}Hz")

            logger.debug("音频降噪处理完成")
            return enhanced_audio

        except Exception as e:
            logger.error(f"音频降噪失败: {e}")
            logger.warning("返回原始音频")
            return audio_data

    def denoise_audio_file(self, input_path: str, output_path: Optional[str] = None) -> str:
        """对音频文件进行降噪处理

        Args:
            input_path: 输入音频文件路径
            output_path: 输出音频文件路径（可选）

        Returns:
            输出文件路径
        """
        if not self.enable:
            logger.warning("降噪功能已禁用")
            return input_path

        try:
            logger.info(f"开始降噪音频文件: {input_path}")

            # 加载音频
            import librosa
            audio_data, sample_rate = librosa.load(input_path, sr=None)

            # 执行降噪
            enhanced_audio = self.denoise_audio(audio_data, int(sample_rate))

            # 确定输出路径
            if output_path is None:
                from pathlib import Path
                input_path_obj = Path(input_path)
                output_path = str(input_path_obj.parent / f"{input_path_obj.stem}_denoised{input_path_obj.suffix}")

            # 保存降噪后的音频
            import soundfile as sf
            sf.write(output_path, enhanced_audio, int(sample_rate))

            logger.info(f"降噪音频已保存到: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"音频文件降噪失败: {e}")
            return input_path

    def is_available(self) -> bool:
        """检查降噪功能是否可用

        Returns:
            降噪功能是否可用
        """
        return self.enable and DEEPFILTERNET_AVAILABLE

    def get_model_info(self) -> dict:
        """获取模型信息

        Returns:
            模型信息字典
        """
        info = {
            "model_name": self.model_name,
            "device": self.device,
            "enabled": self.enable,
            "available": DEEPFILTERNET_AVAILABLE
        }

        if self.enable and self.df_state is not None:
            try:
                # 安全地获取df_state信息
                info.update({
                    "target_sample_rate": self.df_state.sr(),
                })

                # 尝试获取frame_size和hop_size（可能不存在）
                if hasattr(self.df_state, 'frame_size'):
                    info["frame_size"] = self.df_state.frame_size()
                if hasattr(self.df_state, 'hop_size'):
                    info["hop_size"] = self.df_state.hop_size()

            except Exception as e:
                logger.warning(f"获取DeepFilterNet状态信息失败: {e}")
                info["status_error"] = str(e)

        return info

    def unload_model(self):
        """卸载模型释放内存"""
        if self.model is not None:
            del self.model
            self.model = None

        if self.df_state is not None:
            del self.df_state
            self.df_state = None

        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        logger.info("降噪模型已卸载")


# 简化的降噪器（当DeepFilterNet不可用时的备选方案）
class SimpleDenoiser:
    """简化的降噪器（基于谱减法）"""

    def __init__(self):
        """初始化简化降噪器"""
        logger.info("使用简化降噪器（谱减法）")

    def denoise_audio(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """使用简单的谱减法进行降噪

        Args:
            audio_data: 输入音频数据
            sample_rate: 采样率

        Returns:
            降噪后的音频数据
        """
        try:
            # 这里实现一个简单的谱减法降噪
            # 实际应用中可以使用更复杂的算法

            # 简单的高通滤波器去除低频噪声
            try:
                from scipy import signal

                # 设计高通滤波器
                nyquist = sample_rate / 2
                cutoff = 80  # 80Hz截止频率
                normalized_cutoff = cutoff / nyquist

                b, a = signal.butter(4, normalized_cutoff, btype='high')
                filtered_audio = signal.filtfilt(b, a, audio_data)

                logger.debug("使用简化降噪器处理完成")
                return filtered_audio

            except ImportError:
                logger.warning("scipy不可用，跳过简化降噪")
                return audio_data

        except Exception as e:
            logger.error(f"简化降噪失败: {e}")
            return audio_data

    def get_model_info(self) -> dict:
        """获取模型信息

        Returns:
            模型信息字典
        """
        return {
            "type": "simple",
            "method": "high_pass_filter",
            "cutoff_frequency": 80,
            "enabled": True
        }

# 检测设备是cuda的逻辑可以提取出来放在device中
# 这是是不是也没有使用系统配置n参数
