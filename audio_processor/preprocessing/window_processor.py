"""
滑动窗口处理模块
负责将音频分割为重叠的时间窗口
"""

import numpy as np
from typing import List, Tu<PERSON>, Iterator
from loguru import logger


class AudioWindow:
    """音频窗口数据结构"""
    
    def __init__(self, 
                 audio_data: np.ndarray,
                 start_time: float,
                 end_time: float,
                 window_id: int,
                 sample_rate: int):
        """初始化音频窗口
        
        Args:
            audio_data: 窗口音频数据
            start_time: 窗口开始时间（秒）
            end_time: 窗口结束时间（秒）
            window_id: 窗口ID
            sample_rate: 采样率
        """
        self.audio_data = audio_data
        self.start_time = start_time
        self.end_time = end_time
        self.window_id = window_id
        self.sample_rate = sample_rate
        self.duration = end_time - start_time
    
    def __repr__(self):
        return f"AudioWindow(id={self.window_id}, start={self.start_time:.2f}s, end={self.end_time:.2f}s, duration={self.duration:.2f}s)"


class WindowProcessor:
    """滑动窗口处理器"""
    
    def __init__(self, 
                 window_duration: float = 10.0,
                 hop_duration: float = 5.0):
        """初始化窗口处理器
        
        Args:
            window_duration: 窗口持续时间（秒）
            hop_duration: 滑动步长（秒）
        """
        self.window_duration = window_duration
        self.hop_duration = hop_duration
        self.overlap_duration = window_duration - hop_duration
        
        if hop_duration >= window_duration:
            raise ValueError("滑动步长不能大于等于窗口持续时间")
        
        logger.info(f"滑动窗口处理器初始化完成 - 窗口: {window_duration}s, 步长: {hop_duration}s, 重叠: {self.overlap_duration}s")
    
    def create_windows(self, audio_data: np.ndarray, sample_rate: int) -> List[AudioWindow]:
        """创建滑动窗口
        
        Args:
            audio_data: 音频数据
            sample_rate: 采样率
            
        Returns:
            音频窗口列表
        """
        total_duration = len(audio_data) / sample_rate
        logger.info(f"开始创建滑动窗口 - 音频总时长: {total_duration:.2f}s")
        
        windows = []
        window_id = 0
        
        # 计算窗口和步长的样本数
        window_samples = int(self.window_duration * sample_rate)
        hop_samples = int(self.hop_duration * sample_rate)
        
        # 创建滑动窗口
        start_sample = 0
        while start_sample < len(audio_data):
            end_sample = min(start_sample + window_samples, len(audio_data))
            
            # 如果剩余音频太短，跳过
            if end_sample - start_sample < window_samples * 0.5:
                break
            
            # 提取窗口音频数据
            window_audio = audio_data[start_sample:end_sample]
            
            # 计算时间
            start_time = start_sample / sample_rate
            end_time = end_sample / sample_rate
            
            # 创建窗口对象
            window = AudioWindow(
                audio_data=window_audio,
                start_time=start_time,
                end_time=end_time,
                window_id=window_id,
                sample_rate=sample_rate
            )
            
            windows.append(window)
            logger.debug(f"创建窗口 {window_id}: {start_time:.2f}s - {end_time:.2f}s")
            
            window_id += 1
            start_sample += hop_samples
        
        logger.info(f"创建了 {len(windows)} 个滑动窗口")
        return windows
    
    def create_windows_iterator(self, audio_data: np.ndarray, sample_rate: int) -> Iterator[AudioWindow]:
        """创建滑动窗口迭代器（内存友好）
        
        Args:
            audio_data: 音频数据
            sample_rate: 采样率
            
        Yields:
            音频窗口
        """
        total_duration = len(audio_data) / sample_rate
        logger.info(f"开始创建滑动窗口迭代器 - 音频总时长: {total_duration:.2f}s")
        
        window_id = 0
        
        # 计算窗口和步长的样本数
        window_samples = int(self.window_duration * sample_rate)
        hop_samples = int(self.hop_duration * sample_rate)
        
        # 创建滑动窗口
        start_sample = 0
        while start_sample < len(audio_data):
            end_sample = min(start_sample + window_samples, len(audio_data))
            
            # 如果剩余音频太短，跳过
            if end_sample - start_sample < window_samples * 0.5:
                break
            
            # 提取窗口音频数据
            window_audio = audio_data[start_sample:end_sample]
            
            # 计算时间
            start_time = start_sample / sample_rate
            end_time = end_sample / sample_rate
            
            # 创建窗口对象
            window = AudioWindow(
                audio_data=window_audio,
                start_time=start_time,
                end_time=end_time,
                window_id=window_id,
                sample_rate=sample_rate
            )
            
            logger.debug(f"生成窗口 {window_id}: {start_time:.2f}s - {end_time:.2f}s")
            
            yield window
            
            window_id += 1
            start_sample += hop_samples
        
        logger.info(f"窗口迭代器完成，共生成 {window_id} 个窗口")
    
    def get_window_info(self, audio_duration: float) -> dict:
        """获取窗口分割信息
        
        Args:
            audio_duration: 音频总时长（秒）
            
        Returns:
            窗口信息字典
        """
        # 计算窗口数量
        if audio_duration <= self.window_duration:
            num_windows = 1
        else:
            num_windows = int(np.ceil((audio_duration - self.window_duration) / self.hop_duration)) + 1
        
        # 计算总处理时长
        if num_windows == 1:
            total_processed_duration = audio_duration
        else:
            total_processed_duration = self.window_duration + (num_windows - 1) * self.hop_duration
        
        return {
            "audio_duration": audio_duration,
            "window_duration": self.window_duration,
            "hop_duration": self.hop_duration,
            "overlap_duration": self.overlap_duration,
            "num_windows": num_windows,
            "total_processed_duration": total_processed_duration,
            "overlap_ratio": self.overlap_duration / self.window_duration
        }
    
    def validate_window_parameters(self, sample_rate: int) -> bool:
        """验证窗口参数
        
        Args:
            sample_rate: 采样率
            
        Returns:
            参数是否有效
        """
        window_samples = int(self.window_duration * sample_rate)
        hop_samples = int(self.hop_duration * sample_rate)
        
        if window_samples <= 0:
            logger.error(f"窗口样本数无效: {window_samples}")
            return False
        
        if hop_samples <= 0:
            logger.error(f"步长样本数无效: {hop_samples}")
            return False
        
        if hop_samples >= window_samples:
            logger.error(f"步长样本数 ({hop_samples}) 不能大于等于窗口样本数 ({window_samples})")
            return False
        
        return True
