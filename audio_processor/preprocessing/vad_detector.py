"""
Silero VAD语音活动检测模块
使用Silero VAD模型检测语音段
"""

import numpy as np
import torch
from typing import List, Dict, Tuple, Optional
from loguru import logger

try:
    import torchaudio
    TORCHAUDIO_AVAILABLE = True
except ImportError:
    TORCHAUDIO_AVAILABLE = False
    logger.warning("torchaudio 不可用，VAD功能可能受限")


class SpeechSegment:
    """语音段数据结构"""
    
    def __init__(self, start: float, end: float, confidence: float = 1.0):
        """初始化语音段
        
        Args:
            start: 开始时间（秒）
            end: 结束时间（秒）
            confidence: 置信度
        """
        self.start = start
        self.end = end
        self.confidence = confidence
        self.duration = end - start
    
    def __repr__(self):
        return f"SpeechSegment(start={self.start:.3f}s, end={self.end:.3f}s, duration={self.duration:.3f}s, conf={self.confidence:.3f})"
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "start": self.start,
            "end": self.end,
            "duration": self.duration,
            "confidence": self.confidence
        }


class VADDetector:
    """Silero VAD语音活动检测器"""
    
    def __init__(self,
                 model_name: str = "silero_vad",
                 threshold: float = 0.5,
                 min_speech_duration: float = 0.25,
                 min_silence_duration: float = 0.1,
                 window_size_samples: int = 1536,
                 device: str = "cuda"):
        """初始化VAD检测器
        
        Args:
            model_name: VAD模型名称
            threshold: 语音检测阈值
            min_speech_duration: 最小语音段持续时间（秒）
            min_silence_duration: 最小静音段持续时间（秒）
            window_size_samples: 窗口大小（样本数）
            device: 计算设备
        """
        self.model_name = model_name
        self.threshold = threshold
        self.min_speech_duration = min_speech_duration
        self.min_silence_duration = min_silence_duration
        self.window_size_samples = window_size_samples
        self.device = device
        self.model = None
        self.utils = None
        
        self._load_model()
    
    def _load_model(self):
        """加载Silero VAD模型"""
        try:
            logger.info(f"正在加载 {self.model_name} 模型...")
            
            # 加载Silero VAD模型
            self.model, self.utils = torch.hub.load(
                repo_or_dir='snakers4/silero-vad',
                model='silero_vad',
                force_reload=False,
                onnx=False
            )
            
            # 设置设备
            if self.device == "cuda" and torch.cuda.is_available():
                self.model = self.model.to("cuda")
                logger.info("VAD模型已加载到GPU")
            else:
                self.model = self.model.to("cpu")
                logger.info("VAD模型已加载到CPU")
            
            self.model.eval()
            logger.info(f"{self.model_name} 模型加载完成")
            
        except Exception as e:
            logger.error(f"加载VAD模型失败: {e}")
            raise
    
    def detect_speech_segments(self, 
                             audio_data: np.ndarray, 
                             sample_rate: int,
                             window_start_time: float = 0.0) -> List[SpeechSegment]:
        """检测语音段
        
        Args:
            audio_data: 音频数据
            sample_rate: 采样率
            window_start_time: 窗口开始时间（用于计算全局时间）
            
        Returns:
            语音段列表
        """
        try:
            logger.debug(f"开始VAD检测 - 音频长度: {len(audio_data)} 样本, 采样率: {sample_rate}Hz")
            
            # 确保音频是单声道
            if len(audio_data.shape) > 1:
                audio_data = np.mean(audio_data, axis=0)
            
            # 转换为torch tensor
            audio_tensor = torch.from_numpy(audio_data).float()
            
            # 移动到指定设备
            audio_tensor = audio_tensor.to(self.device)
            
            # VAD需要16kHz采样率
            if sample_rate != 16000:
                if TORCHAUDIO_AVAILABLE:
                    # 重采样到16kHz
                    resampler = torchaudio.transforms.Resample(
                        orig_freq=sample_rate, 
                        new_freq=16000
                    ).to(self.device)
                    audio_tensor = resampler(audio_tensor)
                    effective_sample_rate = 16000
                    logger.debug(f"音频已重采样到16kHz用于VAD检测")
                else:
                    logger.warning("torchaudio不可用，无法重采样，VAD结果可能不准确")
                    effective_sample_rate = sample_rate
            else:
                effective_sample_rate = sample_rate
            
            # 使用Silero VAD检测语音段
            speech_timestamps = self.utils[0](
                audio_tensor,
                self.model,
                threshold=self.threshold,
                sampling_rate=effective_sample_rate,
                min_speech_duration_ms=int(self.min_speech_duration * 1000),
                min_silence_duration_ms=int(self.min_silence_duration * 1000),
                window_size_samples=self.window_size_samples,
                speech_pad_ms=30  # 语音段前后填充30ms
            )
            
            # 转换为SpeechSegment对象
            segments = []
            for timestamp in speech_timestamps:
                # 计算相对于原始采样率的时间
                if effective_sample_rate != sample_rate:
                    # 需要调整时间戳
                    start_time = (timestamp['start'] / effective_sample_rate) * sample_rate / sample_rate
                    end_time = (timestamp['end'] / effective_sample_rate) * sample_rate / sample_rate
                else:
                    start_time = timestamp['start'] / effective_sample_rate
                    end_time = timestamp['end'] / effective_sample_rate
                
                # 添加窗口开始时间得到全局时间
                global_start = window_start_time + start_time
                global_end = window_start_time + end_time
                
                segment = SpeechSegment(
                    start=global_start,
                    end=global_end,
                    confidence=timestamp.get('confidence', 1.0)
                )
                segments.append(segment)
            
            logger.debug(f"VAD检测完成，发现 {len(segments)} 个语音段")
            return segments
            
        except Exception as e:
            logger.error(f"VAD检测失败: {e}")
            return []
    
    def detect_speech_in_window(self, 
                              audio_data: np.ndarray, 
                              sample_rate: int,
                              window_start_time: float = 0.0) -> List[Dict]:
        """在窗口中检测语音段（返回字典格式）
        
        Args:
            audio_data: 音频数据
            sample_rate: 采样率
            window_start_time: 窗口开始时间
            
        Returns:
            语音段字典列表，格式: [{'start': 2.3, 'end': 4.8}, ...]
        """
        segments = self.detect_speech_segments(audio_data, sample_rate, window_start_time)
        return [segment.to_dict() for segment in segments]
    
    def get_speech_ratio(self, audio_data: np.ndarray, sample_rate: int) -> float:
        """计算语音占比
        
        Args:
            audio_data: 音频数据
            sample_rate: 采样率
            
        Returns:
            语音占比（0-1）
        """
        segments = self.detect_speech_segments(audio_data, sample_rate)
        
        total_duration = len(audio_data) / sample_rate
        speech_duration = sum(segment.duration for segment in segments)
        
        return speech_duration / total_duration if total_duration > 0 else 0.0
    
    def filter_short_segments(self, segments: List[SpeechSegment]) -> List[SpeechSegment]:
        """过滤过短的语音段
        
        Args:
            segments: 语音段列表
            
        Returns:
            过滤后的语音段列表
        """
        filtered_segments = [
            segment for segment in segments 
            if segment.duration >= self.min_speech_duration
        ]
        
        logger.debug(f"过滤前: {len(segments)} 个语音段，过滤后: {len(filtered_segments)} 个语音段")
        return filtered_segments
    
    def get_model_info(self) -> Dict:
        """获取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            "model_name": self.model_name,
            "threshold": self.threshold,
            "min_speech_duration": self.min_speech_duration,
            "min_silence_duration": self.min_silence_duration,
            "window_size_samples": self.window_size_samples,
            "device": self.device,
            "torchaudio_available": TORCHAUDIO_AVAILABLE
        }
    
    def unload_model(self):
        """卸载模型释放内存"""
        if self.model is not None:
            del self.model
            self.model = None
        
        if self.utils is not None:
            del self.utils
            self.utils = None
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("VAD模型已卸载")
