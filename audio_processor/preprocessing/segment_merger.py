"""
语音段合并模块
负责合并重叠的语音段，避免重复检测
"""

import numpy as np
from typing import List, Tuple, Optional
from loguru import logger

from .vad_detector import SpeechSegment


class MergedSegment:
    """合并后的语音段"""
    
    def __init__(self, 
                 start: float, 
                 end: float, 
                 segment_id: int,
                 source_segments: List[SpeechSegment] = None,
                 confidence: float = 1.0):
        """初始化合并语音段
        
        Args:
            start: 开始时间（秒）
            end: 结束时间（秒）
            segment_id: 段ID
            source_segments: 源语音段列表
            confidence: 置信度
        """
        self.start = start
        self.end = end
        self.segment_id = segment_id
        self.duration = end - start
        self.source_segments = source_segments or []
        self.confidence = confidence
    
    def __repr__(self):
        return f"MergedSegment(id={self.segment_id}, start={self.start:.3f}s, end={self.end:.3f}s, duration={self.duration:.3f}s)"
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "start": self.start,
            "end": self.end,
            "duration": self.duration,
            "segment_id": self.segment_id,
            "confidence": self.confidence,
            "source_count": len(self.source_segments)
        }


class SegmentMerger:
    """语音段合并器"""
    
    def __init__(self,
                 overlap_threshold: float = 0.5,
                 min_segment_duration: float = 0.5,
                 max_segment_duration: float = 30.0):
        """初始化语音段合并器
        
        Args:
            overlap_threshold: 重叠阈值（秒）
            min_segment_duration: 最小段持续时间（秒）
            max_segment_duration: 最大段持续时间（秒）
        """
        self.overlap_threshold = overlap_threshold
        self.min_segment_duration = min_segment_duration
        self.max_segment_duration = max_segment_duration
        
        logger.info(f"语音段合并器初始化完成 - 重叠阈值: {overlap_threshold}s, 最小段: {min_segment_duration}s, 最大段: {max_segment_duration}s")
    
    def merge_segments(self, segments: List[SpeechSegment]) -> List[MergedSegment]:
        """合并重叠的语音段
        
        Args:
            segments: 输入语音段列表
            
        Returns:
            合并后的语音段列表
        """
        if not segments:
            return []
        
        logger.debug(f"开始合并 {len(segments)} 个语音段")
        
        # 按开始时间排序
        sorted_segments = sorted(segments, key=lambda x: x.start)
        
        merged_segments = []
        current_segment = None
        segment_id = 0
        
        for segment in sorted_segments:
            if current_segment is None:
                # 第一个段
                current_segment = MergedSegment(
                    start=segment.start,
                    end=segment.end,
                    segment_id=segment_id,
                    source_segments=[segment],
                    confidence=segment.confidence
                )
            else:
                # 检查是否需要合并
                overlap_duration = self._calculate_overlap(current_segment, segment)
                
                if overlap_duration >= self.overlap_threshold:
                    # 合并段
                    logger.debug(f"合并段: {current_segment.start:.3f}-{current_segment.end:.3f} 与 {segment.start:.3f}-{segment.end:.3f}, 重叠: {overlap_duration:.3f}s")
                    
                    current_segment.end = max(current_segment.end, segment.end)
                    current_segment.duration = current_segment.end - current_segment.start
                    current_segment.source_segments.append(segment)
                    
                    # 更新置信度（取平均值）
                    total_confidence = sum(s.confidence for s in current_segment.source_segments)
                    current_segment.confidence = total_confidence / len(current_segment.source_segments)
                else:
                    # 不合并，保存当前段并开始新段
                    if self._is_valid_segment(current_segment):
                        merged_segments.append(current_segment)
                    
                    segment_id += 1
                    current_segment = MergedSegment(
                        start=segment.start,
                        end=segment.end,
                        segment_id=segment_id,
                        source_segments=[segment],
                        confidence=segment.confidence
                    )
        
        # 添加最后一个段
        if current_segment is not None and self._is_valid_segment(current_segment):
            merged_segments.append(current_segment)
        
        # 分割过长的段
        final_segments = self._split_long_segments(merged_segments)
        
        logger.info(f"语音段合并完成: {len(segments)} -> {len(final_segments)} 个段")
        return final_segments
    
    def _calculate_overlap(self, segment1: MergedSegment, segment2: SpeechSegment) -> float:
        """计算两个段的重叠时长
        
        Args:
            segment1: 第一个段
            segment2: 第二个段
            
        Returns:
            重叠时长（秒）
        """
        overlap_start = max(segment1.start, segment2.start)
        overlap_end = min(segment1.end, segment2.end)
        
        if overlap_start < overlap_end:
            return overlap_end - overlap_start
        else:
            return 0.0
    
    def _is_valid_segment(self, segment: MergedSegment) -> bool:
        """检查段是否有效
        
        Args:
            segment: 语音段
            
        Returns:
            是否有效
        """
        return segment.duration >= self.min_segment_duration
    
    def _split_long_segments(self, segments: List[MergedSegment]) -> List[MergedSegment]:
        """分割过长的语音段
        
        Args:
            segments: 输入段列表
            
        Returns:
            分割后的段列表
        """
        result_segments = []
        segment_id_counter = 0
        
        for segment in segments:
            if segment.duration <= self.max_segment_duration:
                # 段长度合适，直接添加
                segment.segment_id = segment_id_counter
                result_segments.append(segment)
                segment_id_counter += 1
            else:
                # 段太长，需要分割
                logger.debug(f"分割长段: {segment.start:.3f}-{segment.end:.3f} (时长: {segment.duration:.3f}s)")
                
                num_splits = int(np.ceil(segment.duration / self.max_segment_duration))
                split_duration = segment.duration / num_splits
                
                for i in range(num_splits):
                    split_start = segment.start + i * split_duration
                    split_end = min(segment.start + (i + 1) * split_duration, segment.end)
                    
                    split_segment = MergedSegment(
                        start=split_start,
                        end=split_end,
                        segment_id=segment_id_counter,
                        source_segments=segment.source_segments,
                        confidence=segment.confidence
                    )
                    
                    result_segments.append(split_segment)
                    segment_id_counter += 1
        
        return result_segments
    
    def merge_segments_from_windows(self, 
                                  window_segments: List[List[SpeechSegment]]) -> List[MergedSegment]:
        """合并来自多个窗口的语音段
        
        Args:
            window_segments: 每个窗口的语音段列表
            
        Returns:
            合并后的语音段列表
        """
        # 将所有窗口的段合并到一个列表中
        all_segments = []
        for window_segs in window_segments:
            all_segments.extend(window_segs)
        
        logger.info(f"从 {len(window_segments)} 个窗口收集到 {len(all_segments)} 个语音段")
        
        # 执行合并
        return self.merge_segments(all_segments)
    
    def get_merge_statistics(self, 
                           original_segments: List[SpeechSegment],
                           merged_segments: List[MergedSegment]) -> dict:
        """获取合并统计信息
        
        Args:
            original_segments: 原始段列表
            merged_segments: 合并后段列表
            
        Returns:
            统计信息字典
        """
        original_count = len(original_segments)
        merged_count = len(merged_segments)
        
        original_total_duration = sum(seg.duration for seg in original_segments)
        merged_total_duration = sum(seg.duration for seg in merged_segments)
        
        # 计算平均段长度
        avg_original_duration = original_total_duration / original_count if original_count > 0 else 0
        avg_merged_duration = merged_total_duration / merged_count if merged_count > 0 else 0
        
        # 计算合并比例
        merge_ratio = (original_count - merged_count) / original_count if original_count > 0 else 0
        
        return {
            "original_segments": original_count,
            "merged_segments": merged_count,
            "segments_reduced": original_count - merged_count,
            "merge_ratio": merge_ratio,
            "original_total_duration": original_total_duration,
            "merged_total_duration": merged_total_duration,
            "avg_original_duration": avg_original_duration,
            "avg_merged_duration": avg_merged_duration,
            "overlap_threshold": self.overlap_threshold,
            "min_segment_duration": self.min_segment_duration,
            "max_segment_duration": self.max_segment_duration
        }
    
    def validate_merged_segments(self, segments: List[MergedSegment]) -> bool:
        """验证合并后的段是否有效
        
        Args:
            segments: 合并后的段列表
            
        Returns:
            是否有效
        """
        for i, segment in enumerate(segments):
            # 检查段的基本有效性
            if segment.start >= segment.end:
                logger.error(f"段 {i} 开始时间大于等于结束时间: {segment.start} >= {segment.end}")
                return False
            
            if segment.duration < 0:
                logger.error(f"段 {i} 持续时间为负: {segment.duration}")
                return False
            
            # 检查段之间是否有重叠
            if i > 0:
                prev_segment = segments[i-1]
                if segment.start < prev_segment.end:
                    logger.warning(f"段 {i-1} 和段 {i} 存在重叠: {prev_segment.end} > {segment.start}")
        
        return True

# 同样的问题