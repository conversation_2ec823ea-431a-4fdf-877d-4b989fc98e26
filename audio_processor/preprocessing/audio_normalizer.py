"""
音频标准化模块
负责音频重采样、单声道转换和增益归一化
"""

import numpy as np
import librosa
import soundfile as sf
from typing import Tuple, Optional
from pathlib import Path
from loguru import logger


class AudioNormalizer:
    """音频标准化处理器"""
    
    def __init__(self, 
                 target_sample_rate: int = 16000,
                 target_channels: int = 1,
                 normalize_audio: bool = True,
                 normalization_method: str = "peak"):
        """初始化音频标准化器
        
        Args:
            target_sample_rate: 目标采样率
            target_channels: 目标声道数
            normalize_audio: 是否进行音频归一化
            normalization_method: 归一化方法 ("peak", "rms", "lufs")
        """
        self.target_sample_rate = target_sample_rate
        self.target_channels = target_channels
        self.normalize_audio = normalize_audio
        self.normalization_method = normalization_method
        
        logger.info(f"音频标准化器初始化完成 - 采样率: {target_sample_rate}Hz, 声道: {target_channels}")
    
    def normalize_audio_file(self, audio_path: str, output_path: Optional[str] = None) -> Tuple[np.ndarray, int]:
        """标准化音频文件
        
        Args:
            audio_path: 输入音频文件路径
            output_path: 输出音频文件路径（可选）
            
        Returns:
            Tuple[音频数据, 采样率]
        """
        logger.info(f"开始标准化音频文件: {audio_path}")
        
        # 加载音频文件
        audio_data, original_sr = librosa.load(audio_path, sr=None, mono=False)
        
        logger.info(f"原始音频 - 采样率: {original_sr}Hz, 形状: {audio_data.shape}")
        
        # 标准化处理
        normalized_audio, final_sr = self.normalize_audio_data(audio_data, original_sr)
        
        # 保存标准化后的音频（如果指定了输出路径）
        if output_path:
            self._save_audio(normalized_audio, final_sr, output_path)
            logger.info(f"标准化音频已保存到: {output_path}")
        
        return normalized_audio, final_sr
    
    def normalize_audio_data(self, audio_data: np.ndarray, sample_rate: int) -> Tuple[np.ndarray, int]:
        """标准化音频数据
        
        Args:
            audio_data: 音频数据
            sample_rate: 原始采样率
            
        Returns:
            Tuple[标准化后的音频数据, 采样率]
        """
        # 1. 转换为单声道
        if len(audio_data.shape) > 1:
            if self.target_channels == 1:
                audio_data = librosa.to_mono(audio_data)
                logger.debug("音频已转换为单声道")
        
        # 2. 重采样
        if sample_rate != self.target_sample_rate:
            audio_data = librosa.resample(
                audio_data, 
                orig_sr=sample_rate, 
                target_sr=self.target_sample_rate
            )
            logger.debug(f"音频已重采样到 {self.target_sample_rate}Hz")
        
        # 3. 音频归一化
        if self.normalize_audio:
            audio_data = self._normalize_amplitude(audio_data)
            logger.debug(f"音频已使用 {self.normalization_method} 方法归一化")
        
        return audio_data, self.target_sample_rate
    
    def _normalize_amplitude(self, audio_data: np.ndarray) -> np.ndarray:
        """音频幅度归一化
        
        Args:
            audio_data: 音频数据
            
        Returns:
            归一化后的音频数据
        """
        if self.normalization_method == "peak":
            # 峰值归一化
            max_val = np.max(np.abs(audio_data))
            if max_val > 0:
                audio_data = audio_data / max_val
        
        elif self.normalization_method == "rms":
            # RMS归一化
            rms = np.sqrt(np.mean(audio_data ** 2))
            if rms > 0:
                target_rms = 0.1  # 目标RMS值
                audio_data = audio_data * (target_rms / rms)
                # 确保不超过峰值限制
                max_val = np.max(np.abs(audio_data))
                if max_val > 1.0:
                    audio_data = audio_data / max_val
        
        elif self.normalization_method == "lufs":
            # LUFS归一化（简化版本）
            # 这里使用简化的响度归一化
            rms = np.sqrt(np.mean(audio_data ** 2))
            if rms > 0:
                target_lufs = -23.0  # 目标LUFS值
                # 简化的LUFS到线性转换
                target_linear = 10 ** (target_lufs / 20)
                audio_data = audio_data * (target_linear / rms)
                # 确保不超过峰值限制
                max_val = np.max(np.abs(audio_data))
                if max_val > 1.0:
                    audio_data = audio_data / max_val
        
        return audio_data
    
    def _save_audio(self, audio_data: np.ndarray, sample_rate: int, output_path: str):
        """保存音频文件
        
        Args:
            audio_data: 音频数据
            sample_rate: 采样率
            output_path: 输出文件路径
        """
        try:
            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存音频文件
            sf.write(output_path, audio_data, sample_rate)
            
        except Exception as e:
            logger.error(f"保存音频文件失败: {e}")
            raise
    
    def get_audio_info(self, audio_path: str) -> dict:
        """获取音频文件信息
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            音频信息字典
        """
        try:
            info = sf.info(audio_path)
            return {
                "duration": info.duration,
                "sample_rate": info.samplerate,
                "channels": info.channels,
                "frames": info.frames,
                "format": info.format,
                "subtype": info.subtype
            }
        except Exception as e:
            logger.error(f"获取音频信息失败: {e}")
            raise

# 进行归一化后是否覆盖掉了原始音频
# 是否没有使用系统配置的参数，而是硬编码
# 加载音频的函数是不是没有定义，这个模块中都有加载包保存音频的逻辑，是不是可以移动到utils中的i/o模块
# 处理后的音频有没有覆盖掉原始音频文件