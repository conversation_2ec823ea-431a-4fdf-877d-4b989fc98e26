"""
基础处理器类
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from pathlib import Path
from loguru import logger

from .config import ConfigManager


class BaseProcessor(ABC):
    """基础处理器抽象类"""

    def __init__(self, config_name: str, config_manager: Optional[ConfigManager] = None):
        """初始化基础处理器

        Args:
            config_name: 配置名称
            config_manager: 配置管理器（可选）
        """
        self.config_name = config_name
        self.config_manager = config_manager or ConfigManager()
        self.config = self.config_manager.load_config(config_name)
        self._setup_logging()

    def _setup_logging(self):
        """设置模块专用日志"""
        try:
            # 获取日志配置
            logging_config = self.config.get("logging", {})

            if not logging_config:
                logger.info(f"{self.__class__.__name__} 使用默认日志配置")
                return

            # 获取日志文件路径
            log_file = logging_config.get("file")
            if not log_file:
                logger.info(f"{self.__class__.__name__} 未配置专用日志文件")
                return

            # 确保日志目录存在
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)

            # 获取日志配置参数
            log_level = logging_config.get("level", "INFO")
            log_format = logging_config.get("format", "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}")
            rotation = logging_config.get("rotation", "10 MB")
            retention = logging_config.get("retention", "7 days")

            # 创建模块专用的日志记录器
            module_name = self.config_name.upper()

            # 添加模块专用日志文件处理器
            logger.add(
                log_file,
                format=f"{{time:YYYY-MM-DD HH:mm:ss}} | {{level}} | {module_name} | {{message}}",
                level=log_level,
                rotation=rotation,
                retention=retention,
                filter=lambda record: record["extra"].get("module") == self.config_name
            )

            # 创建模块专用的日志记录器
            self.module_logger = logger.bind(module=self.config_name)

            self.module_logger.info(f"{self.__class__.__name__} 初始化完成，日志文件: {log_file}")

        except Exception as e:
            logger.warning(f"设置模块日志失败: {e}，使用默认日志配置")
            self.module_logger = logger
            logger.info(f"{self.__class__.__name__} 初始化完成")

    @abstractmethod
    def process(self, *args, **kwargs) -> Any:
        """处理方法 - 子类必须实现

        Returns:
            处理结果
        """
        pass

    def update_config(self, updates: Dict[str, Any]):
        """更新配置

        Args:
            updates: 更新的配置项
        """
        self.config_manager.update_config(self.config_name, updates)
        self.config = self.config_manager.get_config(self.config_name)
        logger.info(f"配置已更新: {self.config_name}")

    def get_config_value(self, key_path: str, default: Any = None) -> Any:
        """获取配置值

        Args:
            key_path: 配置键路径，如 "model.name"
            default: 默认值

        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config

        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
