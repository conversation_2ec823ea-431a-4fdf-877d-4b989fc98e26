# CAMeL Tools配置文件
# 用于管理camel-tools与主项目的集成

# 环境配置
environment:
  # camel-tools专用环境名称
  conda_env_name: "camel_tools_env"
  
  # Python版本
  python_version: "3.9"
  
  # transformers版本约束
  transformers_version: ">=4.0,<4.44.0"
  
  # 其他依赖
  dependencies:
    - torch
    - numpy
    - pandas
    - requests
    - loguru
    - pyyaml

# API服务配置
api_service:
  # 是否启用camel-tools API服务
  enabled: true
  
  # 服务端口（如果作为独立服务运行）
  port: 8002
  
  # 超时设置（秒）
  timeout: 300
  
  # 临时文件目录
  temp_dir: "/tmp/camel_tools"

# CAMeL Tools功能配置
camel_tools:
  # 默认分词器设置
  tokenizer:
    type: "simple_word_tokenize"
    
  # 形态学分析设置
  morphology:
    database: "builtin"
    max_analyses: 3
    
  # 转写设置
  transliteration:
    default_scheme: "ar2bw"
    available_schemes:
      - "ar2bw"      # Arabic to Buckwalter
      - "bw2ar"      # Buckwalter to Arabic
      - "ar2safebw"  # Arabic to Safe Buckwalter
      - "safebw2ar"  # Safe Buckwalter to Arabic
      - "ar2xmlbw"   # Arabic to XML Buckwalter
      - "xmlbw2ar"   # XML Buckwalter to Arabic

# 日志配置
logging:
  level: "INFO"
  file: "logs/camel_tools.log"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

# 错误处理
error_handling:
  # 最大重试次数
  max_retries: 3
  
  # 重试间隔（秒）
  retry_interval: 1
  
  # 是否在错误时回退到简单处理
  fallback_enabled: true
