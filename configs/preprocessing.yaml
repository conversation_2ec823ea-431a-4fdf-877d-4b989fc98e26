# 音频预处理配置文件

# 是否启用预处理功能
enable: true

# 音频标准化配置
audio:
  target_sample_rate: 16000      # 目标采样率 (Hz)
  target_channels: 1             # 目标声道数 (1=单声道, 2=立体声)
  normalize_audio: true          # 是否进行音频归一化
  normalization_method: "peak"   # 归一化方法: "peak", "rms", "lufs"

# 滑动窗口配置
windowing:
  window_duration: 10.0          # 窗口持续时间 (秒)
  hop_duration: 5.0              # 滑动步长 (秒)
  overlap_threshold: 0.5         # 重叠阈值 (秒)

# 降噪配置
denoising:
  enable: true                   # 是否启用降噪
  model_name: "DeepFilterNet3"   # 降噪模型名称
  device: "cuda"                 # 计算设备: "cuda" 或 "cpu"

# VAD (语音活动检测) 配置
vad:
  model_name: "silero_vad"       # VAD模型名称
  threshold: 0.5                 # 语音检测阈值 (0-1)
  min_speech_duration: 0.25      # 最小语音段持续时间 (秒)
  min_silence_duration: 0.1      # 最小静音段持续时间 (秒)
  window_size_samples: 1536      # VAD窗口大小 (样本数)

# 语音段合并配置
segment_merging:
  overlap_threshold: 0.5         # 重叠阈值 (秒) - 大于此值的重叠段将被合并
  min_segment_duration: 0.5      # 最小段持续时间 (秒)
  max_segment_duration: 30.0     # 最大段持续时间 (秒) - 超过此值的段将被分割

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file: "logs/preprocessing.log"
  rotation: "10 MB"
  retention: "7 days"

# 监控配置
monitoring:
  enable_memory_monitoring: true
  enable_gpu_monitoring: true
  log_interval: 10
