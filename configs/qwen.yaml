# Qwen Q5_0 模型配置文件
# 基于 llama-cpp-python 的配置

model:
  # 模型文件路径
  path: "qwen/Qwen3-32B-Q5_0.gguf"

  # GPU层数 (-1表示全部使用GPU)
  n_gpu_layers: -1

  # 上下文长度 (降低以减少内存使用)
  n_ctx: 8192

  # 批处理大小 (降低以减少内存使用)
  n_batch: 256

  # CPU线程数 (null表示自动检测)
  n_threads: null

  # 其他模型参数
  use_mmap: true
  use_mlock: true
  verbose: false

generation:
  # 最大生成token数 (官方推荐32768，复杂问题38912)
  max_tokens: 32768
  max_tokens_complex: 38912

  # 默认参数 (平衡模式)
  temperature: 0.6
  top_p: 0.95
  top_k: 20
  min_p: 0
  repeat_penalty: 1.0
  presence_penalty: 1.5

  # 停止词
  stop:
    - "<|im_start|>"
    - "<|im_end|>"

# 思维模式配置 (官方推荐)
thinking_mode:
  # 思维模式参数 - 官方最佳实践
  temperature: 0.6
  top_p: 0.95
  top_k: 20
  min_p: 0
  presence_penalty: 1.5
  # 注意：不要使用贪婪解码，会导致性能下降和无限重复

# 非思维模式配置 (官方推荐)
non_thinking_mode:
  # 非思维模式参数 - 官方最佳实践
  temperature: 0.7
  top_p: 0.8
  top_k: 20
  min_p: 0
  presence_penalty: 1.5

# 特定任务配置
task_configs:
  # 数学问题
  math:
    max_tokens: 38912
    temperature: 0.6
    top_p: 0.95
    top_k: 20
    min_p: 0
    presence_penalty: 1.5
    system_prompt: "Please reason step by step, and put your final answer within \\boxed{}."

  # 编程问题
  programming:
    max_tokens: 38912
    temperature: 0.6
    top_p: 0.95
    top_k: 20
    min_p: 0
    presence_penalty: 1.5

  # 多选题
  multiple_choice:
    max_tokens: 32768
    temperature: 0.7
    top_p: 0.8
    top_k: 20
    min_p: 0
    presence_penalty: 1.5
    system_prompt: 'Please show your choice in the answer field with only the choice letter, e.g., "answer": "C".'

# 日志配置
logging:
  level: "INFO"
  file: "logs/qwen.log"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  rotation: "10 MB"
  retention: "7 days"

# 监控配置
monitoring:
  enable_memory_monitoring: true
  enable_gpu_monitoring: true
  log_interval: 10

# 性能优化配置
performance:
  # 预热轮数
  warmup_rounds: 3

  # 批量处理
  batch_processing: true

  # 内存优化
  memory_optimization: true

  # GPU内存分配策略
  gpu_memory_fraction: 0.9

  # 自动清理显存 (每次生成完成后自动清理GPU缓存)
  # 设置为 true 可以在每次生成完成后自动清理显存
  # 设置为 false 可以保持模型在显存中以提高响应速度
  auto_clear_memory: false

  # 智能内存管理 (根据显存使用情况智能清理)
  smart_memory_management: true

  # 显存使用阈值 (超过此阈值时自动清理，单位：GB)
  memory_threshold_gb: 20.0

# 安全配置
safety:
  # 最大输入长度
  max_input_length: 30000

  # 内容过滤
  content_filtering: false

  # 超时设置 (秒)
  timeout: 300
