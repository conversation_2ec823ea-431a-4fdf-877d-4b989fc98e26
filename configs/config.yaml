# Audio Processor 全局配置文件
# 包含所有模块的通用配置

# 全局日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file: "logs/audio_processor.log"
  rotation: "10 MB"
  retention: "7 days"

# 路径配置
paths:
  data_dir: "data"
  output_dir: "output"
  temp_dir: "data/temp"
  logs_dir: "logs"
  configs_dir: "configs"

# 全局监控配置
monitoring:
  enable_memory_monitoring: true
  enable_gpu_monitoring: true
  log_interval: 10  # seconds

# API 配置
api:
  host: "127.0.0.1"
  port: 8000
  debug: false

# 安全配置
security:
  max_file_size: "100MB"  # 最大文件大小
  allowed_extensions: [".wav", ".mp3", ".flac", ".m4a", ".ogg", ".json", ".txt"]
  timeout: 300  # 请求超时时间（秒）
