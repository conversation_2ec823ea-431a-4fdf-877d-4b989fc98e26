#!/usr/bin/env python3
"""
GPU测试脚本 - 验证Whisper模型在GPU上正常运行
"""

import sys
import time
import torch
from pathlib import Path
from loguru import logger

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent / "src"))

from audio_transcriber import AudioTranscriber


def setup_logging():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    logger.add(
        "logs/test_gpu.log",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB"
    )


def check_environment():
    """检查环境"""
    logger.info("=== 环境检查 ===")
    
    # 检查Python版本
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    logger.info(f"Python版本: {python_version}")
    
    # 检查PyTorch
    logger.info(f"PyTorch版本: {torch.__version__}")
    
    # 检查CUDA
    if torch.cuda.is_available():
        logger.info(f"CUDA可用: True")
        logger.info(f"CUDA版本: {torch.version.cuda}")
        logger.info(f"GPU数量: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            memory_gb = torch.cuda.get_device_properties(i).total_memory / 1024**3
            logger.info(f"GPU {i}: {gpu_name} ({memory_gb:.1f}GB)")
            
            # 显示当前GPU内存使用
            memory_allocated = torch.cuda.memory_allocated(i) / 1024**3
            memory_reserved = torch.cuda.memory_reserved(i) / 1024**3
            logger.info(f"GPU {i} 内存: 已分配 {memory_allocated:.2f}GB, 已保留 {memory_reserved:.2f}GB")
        
        return True
    else:
        logger.error("CUDA不可用！")
        return False


def test_model_loading():
    """测试模型加载"""
    logger.info("=== 模型加载测试 ===")
    
    try:
        start_time = time.time()
        
        # 创建转录器（这会加载模型）
        logger.info("开始加载Whisper模型...")
        transcriber = AudioTranscriber("configs/config.yaml")
        
        load_time = time.time() - start_time
        logger.info(f"模型加载完成，耗时: {load_time:.2f}秒")
        
        # 检查模型是否在GPU上
        device = next(transcriber.model.parameters()).device
        logger.info(f"模型设备: {device}")
        
        if device.type == 'cuda':
            logger.info("✓ 模型成功加载到GPU")
            
            # 显示GPU内存使用
            gpu_id = device.index if device.index is not None else 0
            memory_allocated = torch.cuda.memory_allocated(gpu_id) / 1024**3
            memory_reserved = torch.cuda.memory_reserved(gpu_id) / 1024**3
            logger.info(f"模型加载后GPU内存: 已分配 {memory_allocated:.2f}GB, 已保留 {memory_reserved:.2f}GB")
            
            return transcriber
        else:
            logger.error("✗ 模型未加载到GPU")
            return None
            
    except Exception as e:
        logger.error(f"模型加载失败: {e}")
        return None


def test_audio_transcription(transcriber):
    """测试音频转录"""
    logger.info("=== 音频转录测试 ===")
    
    # 查找测试音频文件
    audio_file = "temp/过量（二）.mp3"
    
    if not Path(audio_file).exists():
        logger.error(f"测试音频文件不存在: {audio_file}")
        return False
    
    logger.info(f"使用测试音频: {audio_file}")
    
    try:
        start_time = time.time()
        
        # 记录转录前的GPU状态
        if torch.cuda.is_available():
            gpu_memory_before = torch.cuda.memory_allocated(0) / 1024**3
            logger.info(f"转录前GPU内存: {gpu_memory_before:.2f}GB")
        
        # 执行转录
        logger.info("开始音频转录...")
        result = transcriber.transcribe(audio_file)
        
        transcription_time = time.time() - start_time
        logger.info(f"转录完成，耗时: {transcription_time:.2f}秒")
        
        # 记录转录后的GPU状态
        if torch.cuda.is_available():
            gpu_memory_after = torch.cuda.memory_allocated(0) / 1024**3
            memory_used = gpu_memory_after - gpu_memory_before
            logger.info(f"转录后GPU内存: {gpu_memory_after:.2f}GB (增加 {memory_used:.2f}GB)")
        
        # 显示转录结果
        logger.info("=== 转录结果 ===")
        logger.info(f"音频文件: {result['audio_file']}")
        logger.info(f"音频时长: {result['audio_duration']:.2f}秒")
        logger.info(f"处理时间: {result['processing_time']:.2f}秒")
        logger.info(f"实时率: {result['audio_duration']/result['processing_time']:.2f}x")
        logger.info(f"转录文本: {result['text']}")
        
        # 保存结果
        output_file = "output/test_result.json"
        Path("output").mkdir(exist_ok=True)
        transcriber.save_result(result, output_file)
        logger.info(f"结果已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"音频转录失败: {e}")
        return False


def cleanup_gpu():
    """清理GPU内存"""
    logger.info("=== GPU内存清理 ===")
    
    if torch.cuda.is_available():
        memory_before = torch.cuda.memory_allocated(0) / 1024**3
        logger.info(f"清理前GPU内存: {memory_before:.2f}GB")
        
        torch.cuda.empty_cache()
        
        memory_after = torch.cuda.memory_allocated(0) / 1024**3
        memory_freed = memory_before - memory_after
        logger.info(f"清理后GPU内存: {memory_after:.2f}GB (释放 {memory_freed:.2f}GB)")


def main():
    """主函数"""
    setup_logging()
    
    logger.info("开始GPU测试")
    logger.info("=" * 60)
    
    # 检查环境
    if not check_environment():
        logger.error("环境检查失败，退出测试")
        sys.exit(1)
    
    # 测试模型加载
    transcriber = test_model_loading()
    if transcriber is None:
        logger.error("模型加载失败，退出测试")
        sys.exit(1)
    
    # 测试音频转录
    if not test_audio_transcription(transcriber):
        logger.error("音频转录测试失败")
        sys.exit(1)
    
    # 清理GPU内存
    cleanup_gpu()
    
    logger.info("=" * 60)
    logger.info("🎉 所有测试通过！GPU运行正常！")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("用户中断测试")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        sys.exit(1)
