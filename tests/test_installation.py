#!/usr/bin/env python3
"""
安装验证测试脚本
验证所有依赖是否正确安装
"""

import sys
import importlib
from pathlib import Path

def test_import(module_name, description=""):
    """测试模块导入"""
    try:
        module = importlib.import_module(module_name)
        version = getattr(module, '__version__', 'Unknown')
        print(f"✓ {module_name} ({description}): {version}")
        return True
    except ImportError as e:
        print(f"✗ {module_name} ({description}): 导入失败 - {e}")
        return False

def test_torch_cuda():
    """测试PyTorch和CUDA"""
    try:
        import torch
        print(f"✓ PyTorch版本: {torch.__version__}")

        if torch.cuda.is_available():
            print(f"✓ CUDA可用: {torch.version.cuda}")
            print(f"✓ GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                memory_gb = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"  - GPU {i}: {gpu_name} ({memory_gb:.1f}GB)")
        else:
            print("⚠ CUDA不可用，将使用CPU")

        return True
    except Exception as e:
        print(f"✗ PyTorch测试失败: {e}")
        return False

def test_transformers():
    """测试transformers库"""
    try:
        import transformers
        print(f"✓ Transformers版本: {transformers.__version__}")

        # 测试是否可以加载tokenizer（不下载模型）
        from transformers import AutoProcessor
        print("✓ Transformers功能正常")
        return True
    except Exception as e:
        print(f"✗ Transformers测试失败: {e}")
        return False

def test_audio_libraries():
    """测试音频处理库"""
    success = True

    # 测试librosa
    try:
        import librosa
        print(f"✓ librosa版本: {librosa.__version__}")
    except Exception as e:
        print(f"✗ librosa测试失败: {e}")
        success = False

    # 测试soundfile
    try:
        import soundfile as sf
        print(f"✓ soundfile版本: {sf.__version__}")
    except Exception as e:
        print(f"✗ soundfile测试失败: {e}")
        success = False

    return success

def test_project_structure():
    """测试项目结构"""
    required_dirs = ["src", "configs", "logs", "output", "temp"]
    required_files = ["src/__init__.py", "src/audio_transcriber.py", "src/monitor.py", "configs/config.yaml"]

    success = True

    print("\n检查项目结构:")
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"✓ 目录存在: {dir_name}")
        else:
            print(f"✗ 目录缺失: {dir_name}")
            success = False

    for file_name in required_files:
        if Path(file_name).exists():
            print(f"✓ 文件存在: {file_name}")
        else:
            print(f"✗ 文件缺失: {file_name}")
            success = False

    return success

def test_config_file():
    """测试配置文件"""
    try:
        import yaml
        config_path = "configs/config.yaml"

        if not Path(config_path).exists():
            print(f"✗ 配置文件不存在: {config_path}")
            return False

        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        required_keys = ["logging", "paths", "monitoring", "api", "security"]
        for key in required_keys:
            if key in config:
                print(f"✓ 配置项存在: {key}")
            else:
                print(f"✗ 配置项缺失: {key}")
                return False

        return True
    except Exception as e:
        print(f"✗ 配置文件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("Audio Processor 安装验证测试")
    print("=" * 60)

    all_tests_passed = True

    # 基础库测试
    print("\n1. 基础库测试:")
    basic_modules = [
        ("sys", "系统模块"),
        ("pathlib", "路径处理"),
        ("yaml", "YAML解析"),
        ("loguru", "日志库"),
        ("psutil", "系统监控"),
    ]

    for module, desc in basic_modules:
        if not test_import(module, desc):
            all_tests_passed = False

    # PyTorch和CUDA测试
    print("\n2. PyTorch和CUDA测试:")
    if not test_torch_cuda():
        all_tests_passed = False

    # Transformers测试
    print("\n3. Transformers测试:")
    if not test_transformers():
        all_tests_passed = False

    # 音频处理库测试
    print("\n4. 音频处理库测试:")
    if not test_audio_libraries():
        all_tests_passed = False

    # 可选库测试
    print("\n5. 可选库测试:")
    optional_modules = [
        ("gpustat", "GPU状态监控"),
        ("numpy", "数值计算"),
        ("scipy", "科学计算"),
    ]

    for module, desc in optional_modules:
        test_import(module, desc)  # 可选库失败不影响总体结果

    # 项目结构测试
    print("\n6. 项目结构测试:")
    if not test_project_structure():
        all_tests_passed = False

    # 配置文件测试
    print("\n7. 配置文件测试:")
    if not test_config_file():
        all_tests_passed = False

    # 总结
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 所有测试通过！安装成功！")
        print("\n可以开始使用音频转录工具:")
        print("  python transcribe.py your_audio.wav")
    else:
        print("❌ 部分测试失败，请检查安装")
        print("\n请参考README.md进行故障排除")
    print("=" * 60)

    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
