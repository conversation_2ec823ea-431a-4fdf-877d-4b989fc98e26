# Audio Processor - 多模态音频处理工具包

基于 OpenAI Whisper Large V3 Turbo 和 Qwen3-32B Q5_0 的高性能音频转录和文本处理工具，支持GPU加速，具备完整的系统监控功能。

## 特性

### 音频转录 (Whisper)

- 🚀 **高性能**: 使用 PyTorch Nightly + CUDA 12.8，支持 GPU 加速
- 🎯 **最新模型**: 基于 `openai/whisper-large-v3-turbo` 模型
- 🎵 **多格式支持**: 支持WAV、MP3、FLAC、M4A、OGG等音频格式
- ⏱️ **时间戳支持**: 可选择包含详细时间戳信息
- 📦 **批量处理**: 支持批量转录多个音频文件
- 🎛️ **智能预处理**: 音频标准化、降噪、VAD检测、语音段分割

### 文本处理 (Qwen)

- 🧠 **大语言模型**: 集成Qwen3-32B Q5_0量化模型
- ⚡ **GPU优化**: 使用llama-cpp-python，针对RTX5090优化
- 🤔 **思维模式**: 支持thinking/non-thinking模式切换
- 💬 **对话能力**: 支持多轮对话和角色扮演
- 🌐 **多语言**: 支持100+语言和方言

### 多语言处理 (CAMeL Tools)

- 🔤 **阿拉伯语处理**: 集成CAMeL Tools进行阿拉伯语分词和形态学分析
- 🔄 **环境隔离**: 独立conda环境解决版本冲突问题
- 🌐 **无缝集成**: 与Qwen模型完美配合，支持阿拉伯语翻译流程
- 📝 **转写功能**: 支持阿拉伯语转写和音译

### 系统功能

- 📊 **智能监控**: 实时监控内存和GPU使用情况，支持Whisper和Qwen模型状态跟踪
- 🔧 **模块化日志**: 每个模块独立的日志文件，便于调试和分析
- 🎛️ **统一设备管理**: 智能GPU/CPU设备检测和管理，消除代码重复
- 🔧 **灵活配置**: 支持YAML配置文件自定义参数
- 📝 **多种输出**: 支持JSON、TXT等多种输出格式

## 环境要求

- Python 3.13+
- CUDA 12.8+ (推荐)
- 8GB+ GPU显存 (推荐)
- conda 环境管理器

## 安装

### 1. 创建并激活conda环境

```bash
conda create -n audio_processor_env python=3.13
conda activate audio_processor_env
```

### 2. 安装依赖

```bash
# 安装PyTorch Nightly (CUDA 12.8)
pip install --pre torch torchvision torchaudio --index-url https://download.pytorch.org/whl/nightly/cu128

# 安装其他依赖
pip install -r requirements.txt
```

### 3. 验证安装

```bash
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')"
```

## 项目结构

```text
audio_processor/
├── audio_processor/           # 主要源代码包
│   ├── __init__.py           # 包初始化
│   ├── __main__.py           # 主入口点
│   ├── api/                  # API 服务模块
│   │   ├── __init__.py
│   │   ├── camel_service.py  # CAMeL Tools 服务接口
│   │   ├── models.py         # API 数据模型
│   │   ├── routes.py         # API 路由定义
│   │   └── server.py         # API 服务器
│   ├── cli/                  # 命令行接口
│   │   ├── __init__.py
│   │   ├── main.py           # 统一命令行入口
│   │   ├── qwen_cli.py       # Qwen 文本生成命令
│   │   └── whisper_cli.py    # Whisper 音频转录命令
│   ├── core/                 # 核心功能模块
│   │   ├── __init__.py
│   │   ├── base.py           # 基础处理器类 (含模块化日志)
│   │   └── config.py         # 配置管理器
│   ├── models/               # 模型处理器
│   │   ├── __init__.py
│   │   └── qwen/             # Qwen 文本生成模型
│   │       ├── __init__.py
│   │       └── processor.py  # Qwen 处理器 (集成监控)
│   ├── transcribing/         # 音频转录模块
│   │   ├── __init__.py
│   │   └── transcriber.py    # Whisper 转录器 (集成监控和预处理)
│   ├── preprocessing/        # 音频预处理模块
│   │   ├── __init__.py
│   │   ├── processor.py      # 主预处理器
│   │   ├── audio_normalizer.py # 音频标准化
│   │   ├── window_processor.py # 滑动窗口处理
│   │   ├── denoiser.py       # DeepFilterNet降噪
│   │   ├── vad_detector.py   # Silero VAD检测
│   │   └── segment_merger.py # 语音段合并
│   └── utils/                # 工具模块
│       ├── __init__.py
│       ├── io.py             # 输入输出工具
│       ├── monitor.py        # 系统监控工具 (核心实现)
│       └── device.py         # 设备管理工具 (简化接口)
├── configs/                  # 配置文件目录
│   ├── config.yaml           # 全局配置 (含日志配置)
│   ├── qwen.yaml             # Qwen 模型配置 (含专用日志)
│   ├── whisper.yaml          # Whisper 模型配置 (含专用日志)
│   ├── preprocessing.yaml    # 音频预处理配置
│   └── camel_tools_config.yaml # CAMeL Tools 配置
├── docs/                     # 文档目录
│   ├── MONITORING_GUIDE.md   # 系统监控使用指南
│   ├── QWEN_USAGE_GUIDE.md   # Qwen 使用指南
│   ├── WHISPER_USAGE_GUIDE.md # Whisper 使用指南
│   ├── PREPROCESSING_GUIDE.md # 音频预处理完整指南
│   └── CAMEL_TOOLS_INTEGRATION.md # CAMeL Tools 集成指南
├── examples/                 # 示例代码目录
│   └── camel_tools_example.py # CAMeL Tools 使用示例
├── logs/                     # 日志目录 (模块化日志)
│   ├── audio_processor.log   # 全局日志
│   ├── whisper.log          # Whisper 模块日志
│   ├── qwen.log             # Qwen 模块日志
│   └── system_monitor.log   # 系统监控日志
├── scripts/                  # 脚本目录
│   ├── build_llama_cpp.sh    # 构建脚本
│   ├── cleanup.sh            # 清理脚本
│   ├── install_api_dependencies.sh # API 依赖安装
│   ├── install_llama_cpp_python.sh # llama-cpp-python 安装
│   ├── install_preprocessing_deps.sh # 预处理依赖安装
│   ├── install_rust_and_deepfilternet.sh # Rust 和 DeepFilterNet 安装
│   ├── setup_camel_env.sh    # CAMeL Tools 环境设置
│   ├── test_camel_integration.sh # CAMeL Tools 集成测试
│   ├── test_complete_integration.py # 完整集成测试
│   ├── test_preprocessing.py # 预处理功能测试
│   └── test_*.py             # 其他测试脚本
├── tests/                    # 测试目录
│   ├── test_gpu.py           # GPU 测试
│   └── test_installation.py  # 安装测试
├── data/                     # 数据目录
│   ├── samples/              # 示例文件
│   └── temp/                 # 临时文件
├── output/                   # 输出目录
├── requirements.txt          # Python 依赖
├── setup.py                  # 安装配置
├── README.md                 # 项目说明
├── demo_monitoring.py        # 监控功能演示脚本
├── test_*.py                 # 功能测试脚本
└── *_SUMMARY.md             # 技术文档和重构总结
```

## 快速开始

### 命令行界面

Audio Processor 提供统一的命令行接口，支持两个主要功能：

```bash
# 查看所有可用命令
python -m audio_processor --help

# 查看特定命令的帮助
python -m audio_processor whisper --help
python -m audio_processor qwen --help
```

### 音频转录 (Whisper)

```bash
# 转录单个音频文件
python -m audio_processor whisper audio.wav

# 批量转录
python -m audio_processor whisper *.wav --batch

# 启用音频预处理
python -m audio_processor whisper audio.wav --enable-preprocessing

# 查看完整帮助
python -m audio_processor whisper --help
```

### 文本生成 (Qwen API)

```bash
# 启动 API 服务器
python -m audio_processor qwen

# 指定端口启动
python -m audio_processor qwen --port 9000

# 使用 API 进行文本生成
curl -X POST "http://localhost:8000/api/v1/generate" \
     -H "Content-Type: application/json" \
     -d '{"prompt": "你好，请介绍一下你自己"}'
```

### Python API 使用

```python
# 音频转录 (支持预处理)
from audio_processor.transcribing.transcriber import WhisperTranscriber
transcriber = WhisperTranscriber("whisper", enable_preprocessing=True)
result = transcriber.transcribe("audio.wav")

# 文本生成 (通过 API)
import requests
response = requests.post("http://localhost:8000/api/v1/generate",
                        json={"prompt": "你好"})

# CAMeL Tools 阿拉伯语处理
from audio_processor.api.camel_service import CamelToolsService
camel_service = CamelToolsService()
result = camel_service.tokenize_arabic("مرحبا بكم")
```

## 配置文件

项目使用 YAML 格式的配置文件，支持模块化配置和日志管理：

- **`configs/config.yaml`** - 全局配置（日志、路径、监控、API、安全）
- **`configs/whisper.yaml`** - Whisper 音频转录模型配置（含专用日志）
- **`configs/qwen.yaml`** - Qwen 文本生成模型配置（含专用日志）

### 配置示例

```yaml
# 全局配置示例 (config.yaml)
logging:
  level: "INFO"
  file: "logs/audio_processor.log"

monitoring:
  enable_memory_monitoring: true
  enable_gpu_monitoring: true
  log_interval: 10

api:
  host: "127.0.0.1"
  port: 8000

# 模块配置示例 (whisper.yaml)
logging:
  level: "INFO"
  file: "logs/whisper.log"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  rotation: "10 MB"
  retention: "7 days"

model:
  name: "openai/whisper-large-v3-turbo"
  torch_dtype: "float16"
```

## 📚 详细文档

### 模块使用指南

- **[Whisper 使用指南](docs/WHISPER_USAGE_GUIDE.md)** - 音频转录完整使用说明
- **[Qwen 使用指南](docs/QWEN_USAGE_GUIDE.md)** - 文本生成完整使用说明
- **[系统监控指南](docs/MONITORING_GUIDE.md)** - 系统监控和设备管理使用说明
- **[音频预处理指南](docs/PREPROCESSING_GUIDE.md)** - 音频预处理功能完整使用说明
- **[CAMeL Tools 集成指南](docs/CAMEL_TOOLS_INTEGRATION.md)** - 阿拉伯语处理集成完整指南

### API 文档

- **交互式 API 文档**: 启动服务器后访问 <http://localhost:8000/docs>
- **API 规范**: 查看 `audio_processor/api/` 目录下的代码

### 配置文档

- **全局配置**: `configs/config.yaml` - 日志、路径、监控、API 设置
- **模型配置**: `configs/whisper.yaml` 和 `configs/qwen.yaml` - 含模块专用日志配置
- **预处理配置**: `configs/preprocessing.yaml` - 音频预处理参数配置
- **CAMeL Tools配置**: `configs/camel_tools_config.yaml` - 阿拉伯语处理配置

### 示例代码

- **CAMeL Tools 示例**: `examples/camel_tools_example.py` - 阿拉伯语处理使用示例

## 系统监控

内置完整的智能监控系统：

### 核心功能
- 📊 **实时监控**: 内存和 GPU 使用情况实时跟踪
- 🤖 **模型状态跟踪**: Whisper 和 Qwen 模型生命周期监控
- 📈 **性能统计**: 处理性能统计和基准测试
- 🎛️ **设备管理**: 统一的 GPU/CPU 设备检测和管理

### 模块化日志系统
- 📝 **分离日志**: 每个模块独立的日志文件
- 🔍 **专用监控日志**: 系统监控专门的日志文件
- 📋 **结构化记录**: 详细的日志记录和错误追踪

### 使用示例
```bash
# 查看监控功能演示
python demo_monitoring.py

# 查看各模块日志
tail -f logs/whisper.log      # Whisper 模块日志
tail -f logs/qwen.log         # Qwen 模块日志
tail -f logs/system_monitor.log  # 系统监控日志
```

## 故障排除

### 快速诊断

```bash
# 检查 CUDA 可用性
python -c "import torch; print(f'CUDA: {torch.cuda.is_available()}')"

# 运行系统测试
python tests/test_installation.py

# 检查 GPU 状态
nvidia-smi
```

### 常见问题

详细的故障排除指南请参考各模块的使用文档。

## 开发与测试

### 功能测试

```bash
# 系统集成测试
python tests/test_installation.py           # 安装验证
python tests/test_gpu.py                   # GPU 功能测试

# 音频预处理功能测试
python scripts/test_preprocessing.py        # 预处理功能测试

# CAMeL Tools 集成测试
python scripts/test_camel_integration.sh    # CAMeL Tools 集成测试
python scripts/test_complete_integration.py # 完整集成测试

# 版本兼容性测试
python scripts/test_version_compatibility.py # 版本兼容性测试

# 其他功能测试
python scripts/test_fixes.py               # 修复验证测试
python scripts/test_denoiser_direct.py     # 降噪器直接测试
```

### 代码规范

- 使用 loguru 进行模块化日志管理
- 使用 YAML 格式配置文件
- 使用 pytest 进行单元测试
- 遵循 PEP 8 代码风格
- 消除代码重复，保持 DRY 原则
- 模块化设计，单一职责原则

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.4.0 (2025-01-27)

- 🎵 **音频预处理系统**: 完整的五步音频预处理流程（标准化、滑动窗口、降噪、VAD检测、语音段合并）
- 🔤 **CAMeL Tools 集成**: 阿拉伯语处理能力，支持分词、形态学分析和转写功能
- 🔄 **环境隔离方案**: 解决 transformers 版本冲突，实现 CAMeL Tools 与 Qwen3 共存
- 📁 **模块重构**: 将 Whisper 功能从 models 目录迁移到独立的 transcribing 模块
- 📚 **文档整合**: 合并重复文档，创建完整的使用指南和集成指南
- 🧪 **完整测试**: 新增预处理功能测试和 CAMeL Tools 集成测试

### v1.3.0 (2025-01-27)

- 🔧 **智能监控系统**: 增强的系统监控，支持 Whisper 和 Qwen 模型状态跟踪
- 📝 **模块化日志**: 每个模块独立的日志文件，便于调试和分析
- 🎛️ **统一设备管理**: 消除代码重复，统一的 GPU/CPU 设备检测和管理
- 📊 **专用监控日志**: 系统监控专门的日志文件 (`logs/system_monitor.log`)
- 🔄 **代码重构**: 以 monitor.py 为准，消除 device.py 中的重复代码
- 📚 **文档完善**: 新增监控指南和技术文档

### v1.2.0 (2024-01-01)

- 🔧 **CLI 结构重组**: 统一命令行接口，清晰的模块化设计
- 📁 **文件结构优化**: 删除重复文件，重命名为更清晰的命名
- 🎯 **命令分离**: `whisper` 和 `qwen` 独立子命令
- 📚 **文档更新**: 完整的使用指南和API文档
- ⚙️ **配置优化**: 合并重复配置，分层配置管理

### v1.1.0 (2024-01-01)

- ✅ 集成Qwen3-32B Q5_0量化模型
- ✅ 支持RTX5090 GPU优化
- ✅ 实现thinking/non-thinking模式切换
- ✅ 添加llama-cpp-python绑定
- ✅ 完整的性能基准测试

### v1.0.0 (2024-01-01)

- 初始版本发布
- 支持 Whisper Large V3 Turbo 模型
- 完整的系统监控功能
- 批量处理支持
- 多种输出格式
