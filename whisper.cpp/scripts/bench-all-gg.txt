## M1 Pro (old 22c96b4)

make -j && ./scripts/bench-all.sh 8

Running memcpy benchmark

memcpy:   39.10 GB/s (heat-up)
memcpy:   44.75 GB/s ( 1 thread)
memcpy:   44.78 GB/s ( 1 thread)
memcpy:   44.97 GB/s ( 2 thread)
memcpy:   48.04 GB/s ( 3 thread)
memcpy:   50.55 GB/s ( 4 thread)
memcpy:   55.20 GB/s ( 5 thread)
memcpy:   65.60 GB/s ( 6 thread)
memcpy:   70.64 GB/s ( 7 thread)
memcpy:   73.34 GB/s ( 8 thread)
sum:    -5120002535.000000


make -j && ./scripts/bench-all.sh 1 0 0

Running ggml_mul_mat benchmark with 1 threads

  64 x   64: Q4_0   237.1 GFLOPS (128 runs) | Q4_1   168.6 GFLOPS (128 runs)
  64 x   64: Q5_0   136.4 GFLOPS (128 runs) | Q5_1   135.6 GFLOPS (128 runs) | Q8_0   243.1 GFLOPS (128 runs)
  64 x   64: F16    140.4 GFLOPS (128 runs) | F32    316.6 GFLOPS (128 runs)
 128 x  128: Q4_0   496.6 GFLOPS (128 runs) | Q4_1   348.6 GFLOPS (128 runs)
 128 x  128: Q5_0   273.2 GFLOPS (128 runs) | Q5_1   274.1 GFLOPS (128 runs) | Q8_0   505.1 GFLOPS (128 runs)
 128 x  128: F16    300.4 GFLOPS (128 runs) | F32    653.9 GFLOPS (128 runs)
 256 x  256: Q4_0   791.7 GFLOPS (128 runs) | Q4_1   615.3 GFLOPS (128 runs)
 256 x  256: Q5_0   651.0 GFLOPS (128 runs) | Q5_1   674.7 GFLOPS (128 runs) | Q8_0   803.1 GFLOPS (128 runs)
 256 x  256: F16    869.6 GFLOPS (128 runs) | F32    957.2 GFLOPS (128 runs)
 512 x  512: Q4_0   973.3 GFLOPS (128 runs) | Q4_1   897.9 GFLOPS (128 runs)
 512 x  512: Q5_0  1078.8 GFLOPS (128 runs) | Q5_1   998.4 GFLOPS (128 runs) | Q8_0   752.4 GFLOPS (128 runs)
 512 x  512: F16    892.5 GFLOPS (128 runs) | F32   1399.6 GFLOPS (128 runs)
1024 x 1024: Q4_0  1402.7 GFLOPS (128 runs) | Q4_1  1218.5 GFLOPS (128 runs)
1024 x 1024: Q5_0  1444.8 GFLOPS (128 runs) | Q5_1  1444.7 GFLOPS (128 runs) | Q8_0  1395.7 GFLOPS (128 runs)
1024 x 1024: F16   1524.1 GFLOPS (128 runs) | F32   1726.6 GFLOPS (128 runs)
2048 x 2048: Q4_0  1479.4 GFLOPS ( 87 runs) | Q4_1  1378.5 GFLOPS ( 81 runs)
2048 x 2048: Q5_0  1454.6 GFLOPS ( 85 runs) | Q5_1  1462.9 GFLOPS ( 86 runs) | Q8_0  1483.2 GFLOPS ( 87 runs)
2048 x 2048: F16   1488.0 GFLOPS ( 87 runs) | F32   1538.2 GFLOPS ( 90 runs)
4096 x 4096: Q4_0  1509.7 GFLOPS ( 11 runs) | Q4_1  1433.0 GFLOPS ( 11 runs)
4096 x 4096: Q5_0  1422.4 GFLOPS ( 11 runs) | Q5_1  1437.0 GFLOPS ( 11 runs) | Q8_0  1523.0 GFLOPS ( 12 runs)
4096 x 4096: F16   1551.3 GFLOPS ( 12 runs) | F32   1451.0 GFLOPS ( 11 runs)

|    CPU | Config |         Model |  Th |  FA |    Enc. |    Dec. |    Bch5 |      PP |  Commit |
|    --- |    --- |           --- | --- | --- |     --- |     --- |     --- |     --- |     --- |
| M1 Pro |  METAL |          tiny |   1 |   0 |   39.21 |    1.74 |    0.61 |    0.04 | 22c96b4 |
| M1 Pro |  METAL |          base |   1 |   0 |   70.76 |    2.60 |    0.93 |    0.06 | 22c96b4 |
| M1 Pro |  METAL |         small |   1 |   0 |  217.28 |    6.42 |    2.14 |    0.17 | 22c96b4 |
| M1 Pro |  METAL |        medium |   1 |   0 |  596.74 |   14.43 |    4.75 |    0.45 | 22c96b4 |


make -j && ./scripts/bench-all.sh 1 1 1

|    CPU | Config |         Model |  Th |  FA |    Enc. |    Dec. |    Bch5 |      PP |  Commit |
|    --- |    --- |           --- | --- | --- |     --- |     --- |     --- |     --- |     --- |
| M1 Pro |  METAL |          tiny |   1 |   1 |   30.77 |    1.59 |    0.54 |    0.03 | 22c96b4 |
| M1 Pro |  METAL |          base |   1 |   1 |   60.42 |    2.29 |    0.81 |    0.05 | 22c96b4 |
| M1 Pro |  METAL |         small |   1 |   1 |  183.82 |    5.12 |    1.81 |    0.14 | 22c96b4 |
| M1 Pro |  METAL |        medium |   1 |   1 |  517.92 |   11.60 |    4.01 |    0.38 | 22c96b4 |


## M2 Ultra

make -j && ./scripts/bench-all.sh 8

Running memcpy benchmark

memcpy:   48.01 GB/s (heat-up)
memcpy:   56.00 GB/s ( 1 thread)
memcpy:   56.20 GB/s ( 1 thread)
memcpy:  102.69 GB/s ( 2 thread)
memcpy:  140.32 GB/s ( 3 thread)
memcpy:  179.04 GB/s ( 4 thread)
memcpy:  159.61 GB/s ( 5 thread)
memcpy:  159.02 GB/s ( 6 thread)
memcpy:  180.29 GB/s ( 7 thread)
memcpy:  198.10 GB/s ( 8 thread)
sum:    -5119999345.000000


make -j && ./scripts/bench-all.sh 1

Running ggml_mul_mat benchmark with 1 threads

  64 x   64: Q4_0    37.7 GFLOPS (128 runs) | Q4_1    36.0 GFLOPS (128 runs)
  64 x   64: Q5_0    20.1 GFLOPS (128 runs) | Q5_1    19.8 GFLOPS (128 runs) | Q8_0    39.5 GFLOPS (128 runs)
  64 x   64: F16     29.9 GFLOPS (128 runs) | F32     22.6 GFLOPS (128 runs)
 128 x  128: Q4_0    71.0 GFLOPS (128 runs) | Q4_1    62.2 GFLOPS (128 runs)
 128 x  128: Q5_0    33.4 GFLOPS (128 runs) | Q5_1    31.6 GFLOPS (128 runs) | Q8_0    79.8 GFLOPS (128 runs)
 128 x  128: F16     52.4 GFLOPS (128 runs) | F32     32.7 GFLOPS (128 runs)
 256 x  256: Q4_0    88.6 GFLOPS (128 runs) | Q4_1    77.2 GFLOPS (128 runs)
 256 x  256: Q5_0    40.3 GFLOPS (128 runs) | Q5_1    36.8 GFLOPS (128 runs) | Q8_0   102.5 GFLOPS (128 runs)
 256 x  256: F16     64.6 GFLOPS (128 runs) | F32     36.4 GFLOPS (128 runs)
 512 x  512: Q4_0    94.7 GFLOPS (128 runs) | Q4_1    83.6 GFLOPS (128 runs)
 512 x  512: Q5_0    45.9 GFLOPS (128 runs) | Q5_1    41.3 GFLOPS (128 runs) | Q8_0   112.8 GFLOPS (128 runs)
 512 x  512: F16     72.3 GFLOPS (128 runs) | F32     37.7 GFLOPS (128 runs)
1024 x 1024: Q4_0    98.9 GFLOPS ( 47 runs) | Q4_1    88.2 GFLOPS ( 42 runs)
1024 x 1024: Q5_0    49.0 GFLOPS ( 23 runs) | Q5_1    43.9 GFLOPS ( 21 runs) | Q8_0   121.0 GFLOPS ( 57 runs)
1024 x 1024: F16     72.6 GFLOPS ( 34 runs) | F32     36.0 GFLOPS ( 17 runs)
2048 x 2048: Q4_0   101.3 GFLOPS (  6 runs) | Q4_1    90.0 GFLOPS (  6 runs)
2048 x 2048: Q5_0    50.8 GFLOPS (  3 runs) | Q5_1    45.3 GFLOPS (  3 runs) | Q8_0   124.1 GFLOPS (  8 runs)
2048 x 2048: F16     70.7 GFLOPS (  5 runs) | F32     30.4 GFLOPS (  3 runs)
4096 x 4096: Q4_0   101.7 GFLOPS (  3 runs) | Q4_1    90.3 GFLOPS (  3 runs)
4096 x 4096: Q5_0    52.2 GFLOPS (  3 runs) | Q5_1    45.7 GFLOPS (  3 runs) | Q8_0   123.0 GFLOPS (  3 runs)
4096 x 4096: F16     60.3 GFLOPS (  3 runs) | F32     29.8 GFLOPS (  3 runs)


make -j && ./scripts/bench-all.sh 1 1 0

|      CPU | Config |         Model |  Th |  FA |    Enc. |    Dec. |    Bch5 |      PP |  Commit |
|      --- |    --- |           --- | --- | --- |     --- |     --- |     --- |     --- |     --- |
| M2 ULTRA |  METAL |          tiny |   1 |   0 |    8.74 |    1.20 |    0.36 |    0.01 | ad4e350 |
| M2 ULTRA |  METAL |     tiny-q5_0 |   1 |   0 |   10.30 |    1.15 |    0.38 |    0.01 | ad4e350 |
| M2 ULTRA |  METAL |     tiny-q5_1 |   1 |   0 |   10.71 |    1.13 |    0.38 |    0.01 | ad4e350 |
| M2 ULTRA |  METAL |     tiny-q8_0 |   1 |   0 |    9.97 |    1.12 |    0.37 |    0.01 | ad4e350 |
| M2 ULTRA |  METAL |          base |   1 |   0 |   16.77 |    1.71 |    0.44 |    0.02 | ad4e350 |
| M2 ULTRA |  METAL |     base-q5_0 |   1 |   0 |   16.92 |    1.63 |    0.44 |    0.02 | ad4e350 |
| M2 ULTRA |  METAL |     base-q5_1 |   1 |   0 |   16.84 |    1.63 |    0.44 |    0.02 | ad4e350 |
| M2 ULTRA |  METAL |     base-q8_0 |   1 |   0 |   16.12 |    1.63 |    0.44 |    0.02 | ad4e350 |
| M2 ULTRA |  METAL |         small |   1 |   0 |   45.29 |    3.44 |    0.92 |    0.05 | ad4e350 |
| M2 ULTRA |  METAL |    small-q5_0 |   1 |   0 |   50.43 |    3.34 |    0.94 |    0.06 | ad4e350 |
| M2 ULTRA |  METAL |    small-q5_1 |   1 |   0 |   50.49 |    3.35 |    0.93 |    0.06 | ad4e350 |
| M2 ULTRA |  METAL |    small-q8_0 |   1 |   0 |   47.37 |    3.20 |    0.91 |    0.05 | ad4e350 |
| M2 ULTRA |  METAL |        medium |   1 |   0 |  122.81 |    7.39 |    1.99 |    0.12 | ad4e350 |
| M2 ULTRA |  METAL |   medium-q5_0 |   1 |   0 |  140.62 |    6.73 |    2.03 |    0.14 | ad4e350 |
| M2 ULTRA |  METAL |   medium-q5_1 |   1 |   0 |  140.44 |    6.74 |    2.04 |    0.14 | ad4e350 |
| M2 ULTRA |  METAL |   medium-q8_0 |   1 |   0 |  131.05 |    6.54 |    1.95 |    0.13 | ad4e350 |
| M2 ULTRA |  METAL |    medium-dis |   1 |   0 |  110.95 |    0.99 |    0.24 |    0.02 | ad4e350 |
| M2 ULTRA |  METAL |      large-v2 |   1 |   0 |  222.19 |   10.93 |    3.01 |    0.21 | ad4e350 |
| M2 ULTRA |  METAL | large-v2-q5_0 |   1 |   0 |  258.47 |    9.75 |    3.01 |    0.25 | ad4e350 |
| M2 ULTRA |  METAL | large-v2-q5_1 |   1 |   0 |  258.40 |    9.85 |    3.01 |    0.24 | ad4e350 |
| M2 ULTRA |  METAL | large-v2-q8_0 |   1 |   0 |  236.68 |    9.61 |    2.85 |    0.23 | ad4e350 |
| M2 ULTRA |  METAL |  large-v2-dis |   1 |   0 |  199.28 |    1.12 |    0.27 |    0.02 | ad4e350 |
| M2 ULTRA |  METAL | large-v3-turbo |   1 |   0 |  201.49 |    1.76 |    0.45 |    0.03 | ad4e350 |
| M2 ULTRA |  METAL | large-v3-turbo-q5_0 |   1 |   0 |  233.70 |    1.55 |    0.46 |    0.04 | ad4e350 |
| M2 ULTRA |  METAL | large-v3-turbo-q8_0 |   1 |   0 |  214.20 |    1.51 |    0.44 |    0.04 | ad4e350 |


make -j && ./scripts/bench-all.sh 1 1 1

|      CPU | Config |         Model |  Th |  FA |    Enc. |    Dec. |    Bch5 |      PP |  Commit |
|      --- |    --- |           --- | --- | --- |     --- |     --- |     --- |     --- |     --- |
| M2 ULTRA |  METAL |          tiny |   1 |   1 |    7.82 |    1.31 |    0.35 |    0.01 | ad4e350 |
| M2 ULTRA |  METAL |     tiny-q5_0 |   1 |   1 |    8.32 |    1.28 |    0.37 |    0.01 | ad4e350 |
| M2 ULTRA |  METAL |     tiny-q5_1 |   1 |   1 |    8.21 |    1.28 |    0.37 |    0.01 | ad4e350 |
| M2 ULTRA |  METAL |     tiny-q8_0 |   1 |   1 |    7.97 |    1.23 |    0.36 |    0.01 | ad4e350 |
| M2 ULTRA |  METAL |          base |   1 |   1 |   13.96 |    1.80 |    0.42 |    0.02 | ad4e350 |
| M2 ULTRA |  METAL |     base-q5_0 |   1 |   1 |   15.19 |    1.75 |    0.42 |    0.02 | ad4e350 |
| M2 ULTRA |  METAL |     base-q5_1 |   1 |   1 |   15.09 |    1.75 |    0.42 |    0.02 | ad4e350 |
| M2 ULTRA |  METAL |     base-q8_0 |   1 |   1 |   14.45 |    1.70 |    0.41 |    0.02 | ad4e350 |
| M2 ULTRA |  METAL |         small |   1 |   1 |   40.08 |    3.54 |    0.86 |    0.05 | ad4e350 |
| M2 ULTRA |  METAL |    small-q5_0 |   1 |   1 |   45.07 |    3.51 |    0.88 |    0.05 | ad4e350 |
| M2 ULTRA |  METAL |    small-q5_1 |   1 |   1 |   45.05 |    3.52 |    0.88 |    0.05 | ad4e350 |
| M2 ULTRA |  METAL |    small-q8_0 |   1 |   1 |   42.04 |    3.34 |    0.85 |    0.05 | ad4e350 |
| M2 ULTRA |  METAL |        medium |   1 |   1 |  107.20 |    7.28 |    1.79 |    0.11 | ad4e350 |
| M2 ULTRA |  METAL |   medium-q5_0 |   1 |   1 |  125.02 |    6.67 |    1.83 |    0.12 | ad4e350 |
| M2 ULTRA |  METAL |   medium-q5_1 |   1 |   1 |  124.83 |    6.70 |    1.84 |    0.12 | ad4e350 |
| M2 ULTRA |  METAL |   medium-q8_0 |   1 |   1 |  114.56 |    6.53 |    1.79 |    0.11 | ad4e350 |
| M2 ULTRA |  METAL |    medium-dis |   1 |   1 |   95.96 |    1.01 |    0.23 |    0.01 | ad4e350 |
| M2 ULTRA |  METAL |      large-v2 |   1 |   1 |  194.29 |   10.57 |    2.67 |    0.20 | ad4e350 |
| M2 ULTRA |  METAL | large-v2-q5_0 |   1 |   1 |  230.74 |    9.57 |    2.73 |    0.23 | ad4e350 |
| M2 ULTRA |  METAL | large-v2-q5_1 |   1 |   1 |  229.97 |    9.69 |    2.74 |    0.23 | ad4e350 |
| M2 ULTRA |  METAL | large-v2-q8_0 |   1 |   1 |  208.11 |    9.37 |    2.60 |    0.21 | ad4e350 |
| M2 ULTRA |  METAL |  large-v2-dis |   1 |   1 |  172.72 |    1.12 |    0.26 |    0.02 | ad4e350 |
| M2 ULTRA |  METAL | large-v3-turbo |   1 |   1 |  174.46 |    1.74 |    0.42 |    0.03 | ad4e350 |
| M2 ULTRA |  METAL | large-v3-turbo-q5_0 |   1 |   1 |  205.78 |    1.54 |    0.42 |    0.04 | ad4e350 |
| M2 ULTRA |  METAL | large-v3-turbo-q8_0 |   1 |   1 |  186.33 |    1.50 |    0.40 |    0.03 | ad4e350 |


## M4 Max

make -j && ./scripts/bench-all.sh 8

Running memcpy benchmark

memcpy:   57.23 GB/s (heat-up)
memcpy:   68.85 GB/s ( 1 thread)
memcpy:   70.00 GB/s ( 1 thread)
memcpy:  104.83 GB/s ( 2 thread)
memcpy:  124.54 GB/s ( 3 thread)
memcpy:  144.30 GB/s ( 4 thread)
memcpy:  141.24 GB/s ( 5 thread)
memcpy:  147.03 GB/s ( 6 thread)
memcpy:  147.18 GB/s ( 7 thread)
memcpy:  149.83 GB/s ( 8 thread)
sum:    -5120001475.000000


make -j && ./scripts/bench-all.sh 1

Running ggml_mul_mat benchmark with 1 threads

  64 x   64: Q4_0    49.6 GFLOPS (128 runs) | Q4_1    46.8 GFLOPS (128 runs)
  64 x   64: Q5_0    28.1 GFLOPS (128 runs) | Q5_1    26.8 GFLOPS (128 runs) | Q8_0    52.3 GFLOPS (128 runs)
  64 x   64: F16     38.1 GFLOPS (128 runs) | F32     26.0 GFLOPS (128 runs)
 128 x  128: Q4_0    87.6 GFLOPS (128 runs) | Q4_1    79.9 GFLOPS (128 runs)
 128 x  128: Q5_0    44.7 GFLOPS (128 runs) | Q5_1    41.6 GFLOPS (128 runs) | Q8_0    98.9 GFLOPS (128 runs)
 128 x  128: F16     64.1 GFLOPS (128 runs) | F32     35.4 GFLOPS (128 runs)
 256 x  256: Q4_0   104.2 GFLOPS (128 runs) | Q4_1    92.3 GFLOPS (128 runs)
 256 x  256: Q5_0    57.3 GFLOPS (128 runs) | Q5_1    51.5 GFLOPS (128 runs) | Q8_0   127.7 GFLOPS (128 runs)
 256 x  256: F16     71.4 GFLOPS (128 runs) | F32     40.6 GFLOPS (128 runs)
 512 x  512: Q4_0   109.5 GFLOPS (128 runs) | Q4_1    98.0 GFLOPS (128 runs)
 512 x  512: Q5_0    62.4 GFLOPS (128 runs) | Q5_1    54.6 GFLOPS (128 runs) | Q8_0   135.0 GFLOPS (128 runs)
 512 x  512: F16     82.6 GFLOPS (128 runs) | F32     44.6 GFLOPS (128 runs)
1024 x 1024: Q4_0   112.1 GFLOPS ( 53 runs) | Q4_1   100.9 GFLOPS ( 47 runs)
1024 x 1024: Q5_0    65.4 GFLOPS ( 31 runs) | Q5_1    56.7 GFLOPS ( 27 runs) | Q8_0   140.9 GFLOPS ( 66 runs)
1024 x 1024: F16     88.0 GFLOPS ( 41 runs) | F32     43.4 GFLOPS ( 21 runs)
2048 x 2048: Q4_0   113.4 GFLOPS (  7 runs) | Q4_1   102.0 GFLOPS (  6 runs)
2048 x 2048: Q5_0    67.1 GFLOPS (  4 runs) | Q5_1    57.7 GFLOPS (  4 runs) | Q8_0   142.7 GFLOPS (  9 runs)
2048 x 2048: F16     84.6 GFLOPS (  5 runs) | F32     37.5 GFLOPS (  3 runs)
4096 x 4096: Q4_0   113.8 GFLOPS (  3 runs) | Q4_1   102.0 GFLOPS (  3 runs)
4096 x 4096: Q5_0    67.7 GFLOPS (  3 runs) | Q5_1    58.0 GFLOPS (  3 runs) | Q8_0   142.9 GFLOPS (  3 runs)
4096 x 4096: F16     73.7 GFLOPS (  3 runs) | F32     36.1 GFLOPS (  3 runs)


make -j && ./scripts/bench-all.sh 1 1 0

|    CPU |  Config |         Model |  Th |  FA |    Enc. |    Dec. |    Bch5 |      PP |  Commit |
|    --- |     --- |           --- | --- | --- |     --- |     --- |     --- |     --- |     --- |
| M4 Max |   METAL |          tiny |   1 |   0 |   13.12 |    0.87 |    0.29 |    0.01 | ad4e3509 |
| M4 Max |   METAL |     tiny-q8_0 |   1 |   0 |   15.90 |    0.88 |    0.31 |    0.01 | ad4e3509 |
| M4 Max |   METAL |          base |   1 |   0 |   23.10 |    1.42 |    0.34 |    0.02 | ad4e3509 |
| M4 Max |   METAL |     base-q8_0 |   1 |   0 |   27.25 |    1.31 |    0.34 |    0.02 | ad4e3509 |
| M4 Max |   METAL |         small |   1 |   0 |   71.76 |    3.02 |    0.70 |    0.06 | ad4e3509 |
| M4 Max |   METAL |    small-q8_0 |   1 |   0 |   73.88 |    2.60 |    0.71 |    0.06 | ad4e3509 |
| M4 Max |   METAL |        medium |   1 |   0 |  208.22 |    6.94 |    1.55 |    0.16 | ad4e3509 |
| M4 Max |   METAL |   medium-q8_0 |   1 |   0 |  214.65 |    5.90 |    1.57 |    0.17 | ad4e3509 |
| M4 Max |   METAL |      large-v2 |   1 |   0 |  381.72 |   11.28 |    2.51 |    0.29 | ad4e3509 |
| M4 Max |   METAL | large-v2-q8_0 |   1 |   0 |  394.97 |    8.90 |    2.45 |    0.30 | ad4e3509 |


make -j && ./scripts/bench-all.sh 1 1 1

|    CPU |  Config |         Model |  Th |  FA |    Enc. |    Dec. |    Bch5 |      PP |  Commit |
|    --- |     --- |           --- | --- | --- |     --- |     --- |     --- |     --- |     --- |
| M4 Max |   METAL |          tiny |   1 |   1 |   15.22 |    0.89 |    0.26 |    0.01 | ad4e3509 |
| M4 Max |   METAL |     tiny-q8_0 |   1 |   1 |   14.70 |    0.86 |    0.26 |    0.01 | ad4e3509 |
| M4 Max |   METAL |          base |   1 |   1 |   25.33 |    1.36 |    0.30 |    0.02 | ad4e3509 |
| M4 Max |   METAL |     base-q8_0 |   1 |   1 |   21.27 |    1.31 |    0.30 |    0.02 | ad4e3509 |
| M4 Max |   METAL |         small |   1 |   1 |   58.43 |    2.78 |    0.60 |    0.05 | ad4e3509 |
| M4 Max |   METAL |    small-q8_0 |   1 |   1 |   60.26 |    2.39 |    0.60 |    0.05 | ad4e3509 |
| M4 Max |   METAL |        medium |   1 |   1 |  169.73 |    6.03 |    1.31 |    0.14 | ad4e3509 |
| M4 Max |   METAL |   medium-q8_0 |   1 |   1 |  176.61 |    4.99 |    1.31 |    0.14 | ad4e3509 |
| M4 Max |   METAL |      large-v2 |   1 |   1 |  316.18 |    9.60 |    2.08 |    0.24 | ad4e3509 |
| M4 Max |   METAL | large-v2-q8_0 |   1 |   1 |  329.59 |    7.55 |    2.08 |    0.25 | ad4e3509 |


# V100

WHISPER_CUDA=1 make -j && ./scripts/bench-all.sh 8 1 0

|  GPU |    Config |         Model |  Th |  FA |    Enc. |    Dec. |    Bch5 |      PP |  Commit |
|  --- |       --- |           --- | --- | --- |     --- |     --- |     --- |     --- |     --- |
| V100 | AVX2 CUDA |          tiny |   8 |   0 |    6.15 |    1.02 |    0.30 |    0.01 | ad4e3509 |
| V100 | AVX2 CUDA |     tiny-q5_1 |   8 |   0 |    5.92 |    0.96 |    0.25 |    0.01 | ad4e3509 |
| V100 | AVX2 CUDA |          base |   8 |   0 |   10.60 |    1.43 |    0.43 |    0.02 | ad4e3509 |
| V100 | AVX2 CUDA |     base-q5_1 |   8 |   0 |   10.80 |    1.37 |    0.36 |    0.02 | ad4e3509 |
| V100 | AVX2 CUDA |         small |   8 |   0 |   31.83 |    2.82 |    0.87 |    0.04 | ad4e3509 |
| V100 | AVX2 CUDA |    small-q5_1 |   8 |   0 |   31.88 |    2.68 |    0.72 |    0.04 | ad4e3509 |
| V100 | AVX2 CUDA |        medium |   8 |   0 |   81.30 |    6.02 |    1.81 |    0.09 | ad4e3509 |
| V100 | AVX2 CUDA |   medium-q5_0 |   8 |   0 |   83.21 |    5.44 |    1.41 |    0.10 | ad4e3509 |
| V100 | AVX2 CUDA |      large-v2 |   8 |   0 |  134.81 |    8.64 |    2.69 |    0.14 | ad4e3509 |
| V100 | AVX2 CUDA | large-v2-q5_0 |   8 |   0 |  138.95 |    7.57 |    2.04 |    0.15 | ad4e3509 |
| V100 | AVX2 CUDA | large-v3-turbo |   8 |   0 |  124.42 |    1.37 |    0.43 |    0.02 | ad4e3509 |
| V100 | AVX2 CUDA | large-v3-turbo-q5_0 |   8 |   0 |  127.81 |    1.13 |    0.32 |    0.03 | ad4e3509 |


WHISPER_CUDA=1 make -j && ./scripts/bench-all.sh 8 1 1

|  GPU |    Config |         Model |  Th |  FA |    Enc. |    Dec. |    Bch5 |      PP |  Commit |
|  --- |       --- |           --- | --- | --- |     --- |     --- |     --- |     --- |     --- |
| V100 | AVX2 CUDA |          tiny |   8 |   1 |    4.01 |    0.90 |    0.25 |    0.01 | ad4e3509 |
| V100 | AVX2 CUDA |     tiny-q5_1 |   8 |   1 |    4.12 |    0.88 |    0.18 |    0.01 | ad4e3509 |
| V100 | AVX2 CUDA |          base |   8 |   1 |    7.00 |    1.30 |    0.35 |    0.01 | ad4e3509 |
| V100 | AVX2 CUDA |     base-q5_1 |   8 |   1 |    7.22 |    1.21 |    0.26 |    0.02 | ad4e3509 |
| V100 | AVX2 CUDA |         small |   8 |   1 |   18.68 |    2.39 |    0.69 |    0.03 | ad4e3509 |
| V100 | AVX2 CUDA |    small-q5_1 |   8 |   1 |   19.38 |    2.32 |    0.51 |    0.03 | ad4e3509 |
| V100 | AVX2 CUDA |        medium |   8 |   1 |   53.17 |    5.15 |    1.45 |    0.06 | ad4e3509 |
| V100 | AVX2 CUDA |   medium-q5_0 |   8 |   1 |   55.09 |    4.64 |    1.05 |    0.07 | ad4e3509 |
| V100 | AVX2 CUDA |      large-v2 |   8 |   1 |   85.77 |    7.57 |    2.19 |    0.10 | ad4e3509 |
| V100 | AVX2 CUDA | large-v2-q5_0 |   8 |   1 |   89.24 |    6.48 |    1.48 |    0.11 | ad4e3509 |
| V100 | AVX2 CUDA | large-v3-turbo |   8 |   1 |   75.56 |    1.25 |    0.37 |    0.02 | ad4e3509 |
| V100 | AVX2 CUDA | large-v3-turbo-q5_0 |   8 |   1 |   78.48 |    1.01 |    0.24 |    0.02 | ad4e3509 |
