#!/bin/bash

# 创建camel-tools专用环境的脚本
# 用于解决transformers版本冲突问题

set -e

echo "创建camel-tools专用conda环境..."

# 创建新的conda环境
conda create -n camel_tools_env python=3.9 -y

# 激活环境
source $(conda info --base)/etc/profile.d/conda.sh
conda activate camel_tools_env

# 安装兼容版本的transformers
echo "安装兼容版本的transformers..."
pip install "transformers>=4.0,<4.44.0"

# 安装camel-tools
echo "安装camel-tools..."
pip install camel-tools

# 安装其他必要的依赖
echo "安装其他依赖..."
pip install torch torchvision torchaudio
pip install numpy pandas requests
pip install loguru pyyaml

echo "camel-tools环境设置完成！"
echo "使用方法："
echo "  conda activate camel_tools_env"
echo "  python your_camel_script.py"
echo "  conda deactivate"
