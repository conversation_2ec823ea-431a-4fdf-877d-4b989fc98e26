#!/usr/bin/env python3
"""
测试修复后的功能
"""

import sys
import os
import tempfile
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_ffmpeg():
    """测试ffmpeg是否可用"""
    print("🔧 测试ffmpeg...")
    
    import subprocess
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        print('✅ ffmpeg已安装并可用')
        print(f'   版本: {result.stdout.split()[2]}')
        return True
    except FileNotFoundError:
        print('❌ ffmpeg未找到')
        return False
    except Exception as e:
        print(f'⚠️  ffmpeg测试失败: {e}')
        return False

def test_denoiser_basic():
    """测试降噪器基本功能"""
    print("\n🔧 测试降噪器基本功能...")
    
    try:
        from audio_processor.preprocessing.denoiser import AudioDenoiser
        
        # 创建测试音频数据
        test_audio = np.random.randn(16000).astype(np.float32)  # 1秒的随机音频
        
        # 测试禁用状态
        denoiser = AudioDenoiser(enable=False)
        result = denoiser.denoise_audio(test_audio, 16000)
        
        print('✅ 降噪器基本功能正常')
        print(f'   输入形状: {test_audio.shape}')
        print(f'   输出形状: {result.shape}')
        print(f'   数据类型: {result.dtype}')
        
        return True
        
    except Exception as e:
        print(f'❌ 降噪器测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_whisper_with_ffmpeg():
    """测试Whisper与ffmpeg的集成"""
    print("\n🔧 测试Whisper与ffmpeg集成...")
    
    try:
        from audio_processor.models.whisper.transcriber import WhisperTranscriber
        import soundfile as sf
        
        # 创建测试音频文件
        test_audio = np.sin(2 * np.pi * 440 * np.linspace(0, 1, 16000)).astype(np.float32)
        
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_path = temp_file.name
            sf.write(temp_path, test_audio, 16000)
            
            try:
                # 测试Whisper转录
                transcriber = WhisperTranscriber("whisper")
                result = transcriber.transcribe(temp_path)
                
                print('✅ Whisper与ffmpeg集成正常')
                print(f'   转录结果类型: {type(result)}')
                if result and 'text' in result:
                    print(f'   转录文本长度: {len(result["text"])}')
                
                return True
                
            finally:
                # 清理临时文件
                os.unlink(temp_path)
                
    except Exception as e:
        print(f'❌ Whisper与ffmpeg集成测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_preprocessing_integration():
    """测试预处理集成功能"""
    print("\n🔧 测试预处理集成功能...")
    
    try:
        from audio_processor.preprocessing.audio_normalizer import AudioNormalizer
        from audio_processor.preprocessing.vad_detector import VADDetector
        
        # 创建测试音频
        test_audio = np.random.randn(32000).astype(np.float32)  # 2秒音频
        
        # 测试音频标准化
        normalizer = AudioNormalizer()
        normalized_audio, sample_rate = normalizer.normalize_audio_data(test_audio, 44100)
        
        print('✅ 音频标准化正常')
        print(f'   原始: {test_audio.shape}, 44100Hz')
        print(f'   标准化: {normalized_audio.shape}, {sample_rate}Hz')
        
        # 测试VAD检测
        vad_detector = VADDetector()
        segments = vad_detector.detect_speech_segments(normalized_audio, sample_rate)
        
        print('✅ VAD检测正常')
        print(f'   检测到 {len(segments)} 个语音段')
        
        return True
        
    except Exception as e:
        print(f'❌ 预处理集成测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 修复功能测试")
    print("=" * 50)
    
    results = []
    
    # 运行各项测试
    results.append(("ffmpeg", test_ffmpeg()))
    results.append(("降噪器基本功能", test_denoiser_basic()))
    results.append(("Whisper与ffmpeg集成", test_whisper_with_ffmpeg()))
    results.append(("预处理集成功能", test_preprocessing_integration()))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("🎯 测试结果总结:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有修复功能测试通过！")
    else:
        print("⚠️  部分测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
