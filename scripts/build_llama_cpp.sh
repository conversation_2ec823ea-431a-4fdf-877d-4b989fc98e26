#!/bin/bash

# Llama.cpp 编译脚本 - 针对RTX5090优化
# 支持sm_120架构和所有性能优化选项

set -e

echo "=== Llama.cpp 编译脚本 (RTX5090优化) ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
LLAMA_CPP_DIR="llama.cpp"
LLAMA_CPP_REPO="https://github.com/ggerganov/llama.cpp.git"
BUILD_DIR="build"

# 检查环境
check_environment() {
    log_info "检查编译环境..."

    # 检查CUDA
    if ! command -v nvcc &> /dev/null; then
        log_error "NVCC not found. Please install CUDA toolkit."
        exit 1
    fi

    # 检查cmake
    if ! command -v cmake &> /dev/null; then
        log_error "CMake not found. Please install cmake."
        exit 1
    fi

    log_success "环境检查通过"
}

# 克隆或更新llama.cpp
setup_source() {
    log_info "设置llama.cpp源码..."

    if [ -d "$LLAMA_CPP_DIR" ]; then
        log_warning "llama.cpp目录已存在，将更新到最新版本..."
        cd "$LLAMA_CPP_DIR"
        git pull origin master
        cd ..
    else
        log_info "克隆llama.cpp仓库..."
        git clone "$LLAMA_CPP_REPO" "$LLAMA_CPP_DIR"
    fi

    log_success "源码准备完成"
}

# 配置编译选项
configure_build() {
    log_info "配置编译选项..."

    cd "$LLAMA_CPP_DIR"

    # 清理之前的构建
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"

    log_info "CMake配置 - RTX5090优化..."

    # RTX5090优化的CMake配置 (CUDA 12.8支持sm_120)
    cmake .. \
        -DCMAKE_BUILD_TYPE=Release \
        -DGGML_CUDA=ON \
        -DCMAKE_CUDA_ARCHITECTURES="70;75;80;86;90;120" \
        -DGGML_CUDA_F16=ON \
        -DGGML_NATIVE=ON \
        -DGGML_K_QUANTS=ON \
        -DGGML_QKK_64=ON \
        -DGGML_CUDA_FORCE_DMMV=ON \
        -DGGML_CUDA_FORCE_MMQ=ON \
        -DGGML_CUDA_FA_ALL_QUANTS=ON \
        -DCMAKE_CUDA_FLAGS="-arch=sm_120 -gencode=arch=compute_70,code=sm_70 -gencode=arch=compute_75,code=sm_75 -gencode=arch=compute_80,code=sm_80 -gencode=arch=compute_86,code=sm_86 -gencode=arch=compute_90,code=sm_90 -gencode=arch=compute_120,code=sm_120" \
        -DCMAKE_CXX_FLAGS="-O3 -march=native -mtune=native" \
        -DCMAKE_C_FLAGS="-O3 -march=native -mtune=native"

    log_success "CMake配置完成"
}

# 编译
build_llama_cpp() {
    log_info "开始编译llama.cpp..."

    # 获取CPU核心数用于并行编译
    NPROC=$(nproc)
    log_info "使用 $NPROC 个CPU核心进行并行编译..."

    # 编译
    make -j"$NPROC"

    log_success "编译完成!"
}

# 验证编译结果
verify_build() {
    log_info "验证编译结果..."

    # 检查关键可执行文件
    EXECUTABLES=("llama-cli" "llama-server" "llama-bench")

    for exe in "${EXECUTABLES[@]}"; do
        if [ -f "./$exe" ]; then
            log_success "✓ $exe 编译成功"
        else
            log_error "✗ $exe 编译失败"
            exit 1
        fi
    done

    # 测试CUDA支持
    log_info "测试CUDA支持..."
    if ./llama-cli --help | grep -q "gpu-layers"; then
        log_success "✓ CUDA支持已启用"
    else
        log_warning "⚠ CUDA支持可能未正确启用"
    fi

    log_success "编译验证完成!"
}

# 创建符号链接
create_links() {
    log_info "创建可执行文件链接..."

    cd ../../  # 回到项目根目录

    # 创建bin目录
    mkdir -p bin

    # 创建符号链接
    ln -sf "../$LLAMA_CPP_DIR/$BUILD_DIR/llama-cli" bin/llama-cli
    ln -sf "../$LLAMA_CPP_DIR/$BUILD_DIR/llama-server" bin/llama-server
    ln -sf "../$LLAMA_CPP_DIR/$BUILD_DIR/llama-bench" bin/llama-bench

    log_success "符号链接创建完成"
}

# 显示编译信息
show_build_info() {
    log_info "编译信息摘要:"
    echo "=================================="
    echo "编译目录: $LLAMA_CPP_DIR/$BUILD_DIR"
    echo "CUDA架构: 70;75;80;86;90;120 (包含RTX5090)"
    echo "优化选项: CUBLAS, F16, Native, K-Quants, QKK_64"
    echo "可执行文件位置: ./bin/"
    echo "=================================="
    echo ""
    log_info "接下来可以运行测试脚本:"
    echo "./test_model.sh"
}

# 主函数
main() {
    log_info "开始编译llama.cpp..."

    check_environment
    setup_source
    configure_build
    build_llama_cpp
    verify_build
    create_links
    show_build_info

    log_success "llama.cpp编译完成!"
}

# 执行主函数
main "$@"
