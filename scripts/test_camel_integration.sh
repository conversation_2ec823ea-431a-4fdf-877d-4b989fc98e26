#!/bin/bash

# CAMeL Tools集成测试脚本
# 用于验证CAMeL Tools是否正确安装和配置

set -e

echo "🚀 开始CAMeL Tools集成测试"
echo "=" * 50

# 检查conda环境是否存在
echo "📋 检查conda环境..."
if conda env list | grep -q "camel_tools_env"; then
    echo "✅ camel_tools_env环境存在"
else
    echo "❌ camel_tools_env环境不存在，请先运行 ./scripts/setup_camel_env.sh"
    exit 1
fi

# 测试基本导入
echo "🔍 测试基本导入..."
conda run -n camel_tools_env python -c "
import camel_tools
import transformers
print(f'✅ CAMeL Tools版本: {camel_tools.__version__}')
print(f'✅ Transformers版本: {transformers.__version__}')
"

# 测试分词功能
echo "🔤 测试阿拉伯语分词..."
conda run -n camel_tools_env python -c "
from camel_tools.tokenizers.word import simple_word_tokenize
text = 'مرحبا بكم في العالم'
tokens = simple_word_tokenize(text)
print(f'原文: {text}')
print(f'分词: {tokens}')
print(f'词数: {len(tokens)}')
print('✅ 分词功能正常')
"

# 测试API服务
echo "🌐 测试API服务..."
cd "$(dirname "$0")/.."
python -c "
import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

from audio_processor.api.camel_service import CamelToolsService

camel_service = CamelToolsService()
result = camel_service.check_environment()

if result.get('success'):
    print('✅ API服务正常')
    print(f'   CAMeL Tools: {result[\"data\"][\"camel_tools_version\"]}')
    print(f'   Transformers: {result[\"data\"][\"transformers_version\"]}')
else:
    print(f'❌ API服务异常: {result.get(\"error\")}')
    sys.exit(1)

# 测试分词API
tokenize_result = camel_service.tokenize_arabic('مرحبا')
if tokenize_result.get('success'):
    print('✅ 分词API正常')
    print(f'   结果: {tokenize_result[\"data\"][\"tokens\"]}')
else:
    print(f'❌ 分词API异常: {tokenize_result.get(\"error\")}')
"

echo ""
echo "🎉 所有测试通过！CAMeL Tools集成成功！"
echo ""
echo "📚 使用方法:"
echo "   from audio_processor.api.camel_service import CamelToolsService"
echo "   camel_service = CamelToolsService()"
echo "   result = camel_service.tokenize_arabic('مرحبا بكم')"
echo ""
