#!/usr/bin/env python3
"""
直接测试DeepFilterNet降噪功能的修复
"""

import sys
import numpy as np
import torch
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_deepfilternet_direct():
    """直接测试DeepFilterNet API"""
    print("🔧 直接测试DeepFilterNet API")
    print("=" * 50)
    
    try:
        # 导入DeepFilterNet
        from df import enhance, init_df
        
        print("✅ DeepFilterNet导入成功")
        
        # 初始化模型
        print("🔄 初始化DeepFilterNet模型...")
        model, df_state, _ = init_df()
        print("✅ 模型初始化成功")
        
        # 创建测试音频
        sample_rate = df_state.sr()  # 使用模型的原生采样率
        duration = 1.0
        samples = int(sample_rate * duration)
        
        # 创建正弦波 + 噪声
        t = np.linspace(0, duration, samples, False)
        signal = np.sin(2 * np.pi * 440 * t)  # 440Hz正弦波
        noise = np.random.normal(0, 0.1, samples)  # 添加噪声
        noisy_audio = (signal + noise).astype(np.float32)
        
        print(f"📊 测试音频信息:")
        print(f"   采样率: {sample_rate}Hz")
        print(f"   时长: {duration}秒")
        print(f"   样本数: {samples}")
        print(f"   音频形状: {noisy_audio.shape}")
        print(f"   数据类型: {noisy_audio.dtype}")
        
        # 转换为torch tensor
        print("\n🔄 转换为torch tensor...")
        audio_tensor = torch.from_numpy(noisy_audio).float()
        
        # 检查设备
        device = next(model.parameters()).device
        print(f"   模型设备: {device}")
        
        # 移动到正确设备
        audio_tensor = audio_tensor.to(device)
        
        # 添加通道维度 [C, T]
        if audio_tensor.dim() == 1:
            audio_tensor = audio_tensor.unsqueeze(0)  # (1, samples)
        
        print(f"   输入tensor形状: {audio_tensor.shape}")
        print(f"   输入tensor设备: {audio_tensor.device}")
        
        # 执行降噪
        print("\n🔄 执行降噪...")
        with torch.no_grad():
            enhanced_tensor = enhance(
                model=model,
                df_state=df_state,
                audio=audio_tensor,
                pad=True
            )
        
        print(f"   输出tensor形状: {enhanced_tensor.shape}")
        print(f"   输出tensor设备: {enhanced_tensor.device}")
        print(f"   输出tensor类型: {type(enhanced_tensor)}")
        
        # 转换回numpy - 这是关键步骤
        print("\n🔄 转换回numpy...")
        
        # 方法1: 标准转换
        try:
            if enhanced_tensor.is_cuda:
                enhanced_tensor_cpu = enhanced_tensor.cpu()
            else:
                enhanced_tensor_cpu = enhanced_tensor
            
            enhanced_tensor_detached = enhanced_tensor_cpu.detach()
            enhanced_numpy = enhanced_tensor_detached.numpy()
            
            print("✅ 标准转换成功")
            print(f"   numpy形状: {enhanced_numpy.shape}")
            print(f"   numpy类型: {enhanced_numpy.dtype}")
            
            # 移除通道维度
            if enhanced_numpy.shape[0] == 1:
                enhanced_numpy = enhanced_numpy.squeeze(0)
            
            print(f"   最终形状: {enhanced_numpy.shape}")
            
            # 计算信噪比改善
            signal_power = np.mean(signal ** 2)
            noise_power_before = np.mean((noisy_audio - signal) ** 2)
            noise_power_after = np.mean((enhanced_numpy - signal) ** 2)
            
            snr_before = 10 * np.log10(signal_power / noise_power_before)
            snr_after = 10 * np.log10(signal_power / noise_power_after)
            snr_improvement = snr_after - snr_before
            
            print(f"\n📈 降噪效果:")
            print(f"   降噪前SNR: {snr_before:.2f} dB")
            print(f"   降噪后SNR: {snr_after:.2f} dB")
            print(f"   SNR改善: {snr_improvement:.2f} dB")
            
            return True
            
        except Exception as e:
            print(f"❌ 标准转换失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_our_denoiser():
    """测试我们的降噪器实现"""
    print("\n🔧 测试我们的降噪器实现")
    print("=" * 50)
    
    try:
        from audio_processor.preprocessing.denoiser import AudioDenoiser
        
        # 创建测试音频
        sample_rate = 16000
        duration = 1.0
        samples = int(sample_rate * duration)
        
        t = np.linspace(0, duration, samples, False)
        signal = np.sin(2 * np.pi * 440 * t)
        noise = np.random.normal(0, 0.1, samples)
        noisy_audio = (signal + noise).astype(np.float32)
        
        print(f"📊 测试音频: {noisy_audio.shape}, {sample_rate}Hz")
        
        # 测试降噪器
        denoiser = AudioDenoiser(enable=True)
        
        if denoiser.is_available():
            print("✅ 降噪器可用")
            
            result = denoiser.denoise_audio(noisy_audio, sample_rate)
            
            print(f"✅ 降噪完成")
            print(f"   输入: {noisy_audio.shape}")
            print(f"   输出: {result.shape}")
            print(f"   类型: {type(result)}")
            
            return True
        else:
            print("⚠️  降噪器不可用")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 DeepFilterNet直接测试")
    print("=" * 60)
    
    # 测试1: 直接API
    success1 = test_deepfilternet_direct()
    
    # 测试2: 我们的实现
    success2 = test_our_denoiser()
    
    print("\n" + "=" * 60)
    print("🎯 测试结果:")
    print(f"   直接API测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"   我们的实现: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("🎉 所有测试通过！")
    elif success1:
        print("⚠️  直接API正常，我们的实现有问题")
    else:
        print("❌ 需要进一步调试")

if __name__ == "__main__":
    main()
