#!/bin/bash
"""
安装 Qwen API 依赖
"""

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Python 环境
check_python() {
    log_info "检查 Python 环境..."
    
    if ! command -v python &> /dev/null; then
        log_error "Python 未安装或不在 PATH 中"
        exit 1
    fi
    
    PYTHON_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
    log_success "Python 版本: $PYTHON_VERSION"
    
    # 检查是否在虚拟环境中
    if [[ "$VIRTUAL_ENV" != "" ]]; then
        log_success "检测到虚拟环境: $VIRTUAL_ENV"
    elif [[ "$CONDA_DEFAULT_ENV" != "" ]]; then
        log_success "检测到 Conda 环境: $CONDA_DEFAULT_ENV"
    else
        log_warning "未检测到虚拟环境，建议在虚拟环境中安装"
    fi
}

# 安装 FastAPI 相关依赖
install_fastapi() {
    log_info "安装 FastAPI 和相关依赖..."
    
    pip install fastapi>=0.104.0 || {
        log_error "FastAPI 安装失败"
        exit 1
    }
    
    pip install "uvicorn[standard]>=0.24.0" || {
        log_error "Uvicorn 安装失败"
        exit 1
    }
    
    pip install pydantic>=2.5.0 || {
        log_error "Pydantic 安装失败"
        exit 1
    }
    
    log_success "FastAPI 依赖安装完成"
}

# 安装其他依赖
install_other_deps() {
    log_info "安装其他依赖..."
    
    pip install requests>=2.32.0 || {
        log_error "Requests 安装失败"
        exit 1
    }
    
    log_success "其他依赖安装完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    python -c "import fastapi; print(f'FastAPI 版本: {fastapi.__version__}')" || {
        log_error "FastAPI 导入失败"
        exit 1
    }
    
    python -c "import uvicorn; print(f'Uvicorn 版本: {uvicorn.__version__}')" || {
        log_error "Uvicorn 导入失败"
        exit 1
    }
    
    python -c "import pydantic; print(f'Pydantic 版本: {pydantic.__version__}')" || {
        log_error "Pydantic 导入失败"
        exit 1
    }
    
    log_success "所有依赖验证通过"
}

# 主函数
main() {
    echo "🚀 开始安装 Qwen API 依赖..."
    echo ""
    
    # 检查是否在项目根目录
    if [ ! -f "README.md" ] || [ ! -d "audio_processor" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    check_python
    echo ""
    
    install_fastapi
    echo ""
    
    install_other_deps
    echo ""
    
    verify_installation
    echo ""
    
    log_success "🎉 Qwen API 依赖安装完成！"
    echo ""
    log_info "下一步:"
    echo "1. 启动 API 服务器: python qwen_api_server.py"
    echo "2. 测试 API 功能: python scripts/test_qwen_api.py"
    echo "3. 查看 API 文档: http://localhost:8000/docs"
}

# 运行主函数
main "$@"
