#!/usr/bin/env python3
"""
测试修复后的DeepFilterNet降噪功能
"""

import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_denoiser_fix():
    """测试修复后的降噪器"""
    print("🔧 测试修复后的DeepFilterNet降噪功能")
    print("=" * 50)
    
    try:
        from audio_processor.preprocessing.denoiser import AudioDenoiser
        
        # 创建测试音频数据（1秒，16kHz）
        sample_rate = 16000
        duration = 1.0
        samples = int(sample_rate * duration)
        
        # 创建一个简单的正弦波信号 + 噪声
        t = np.linspace(0, duration, samples, False)
        signal = np.sin(2 * np.pi * 440 * t)  # 440Hz正弦波
        noise = np.random.normal(0, 0.1, samples)  # 添加噪声
        noisy_audio = (signal + noise).astype(np.float32)
        
        print(f"📊 测试音频信息:")
        print(f"   采样率: {sample_rate}Hz")
        print(f"   时长: {duration}秒")
        print(f"   样本数: {samples}")
        print(f"   音频形状: {noisy_audio.shape}")
        print(f"   数据类型: {noisy_audio.dtype}")
        print(f"   音频范围: [{noisy_audio.min():.3f}, {noisy_audio.max():.3f}]")
        
        # 测试1: 禁用状态
        print("\n🧪 测试1: 降噪器禁用状态")
        denoiser_disabled = AudioDenoiser(enable=False)
        result_disabled = denoiser_disabled.denoise_audio(noisy_audio, sample_rate)
        
        print(f"   ✅ 禁用状态测试通过")
        print(f"   输入形状: {noisy_audio.shape}")
        print(f"   输出形状: {result_disabled.shape}")
        print(f"   数据相同: {np.array_equal(noisy_audio, result_disabled)}")
        
        # 测试2: 启用状态（实际降噪）
        print("\n🧪 测试2: 降噪器启用状态")
        try:
            denoiser_enabled = AudioDenoiser(enable=True)
            
            print(f"   模型信息: {denoiser_enabled.get_model_info()}")
            
            if denoiser_enabled.is_available():
                print("   🔄 开始降噪处理...")
                result_enabled = denoiser_enabled.denoise_audio(noisy_audio, sample_rate)
                
                print(f"   ✅ 启用状态测试通过")
                print(f"   输入形状: {noisy_audio.shape}")
                print(f"   输出形状: {result_enabled.shape}")
                print(f"   输出范围: [{result_enabled.min():.3f}, {result_enabled.max():.3f}]")
                
                # 计算信噪比改善
                signal_power = np.mean(signal ** 2)
                noise_power_before = np.mean((noisy_audio - signal) ** 2)
                noise_power_after = np.mean((result_enabled - signal) ** 2)
                
                snr_before = 10 * np.log10(signal_power / noise_power_before)
                snr_after = 10 * np.log10(signal_power / noise_power_after)
                snr_improvement = snr_after - snr_before
                
                print(f"   📈 信噪比分析:")
                print(f"     降噪前SNR: {snr_before:.2f} dB")
                print(f"     降噪后SNR: {snr_after:.2f} dB")
                print(f"     SNR改善: {snr_improvement:.2f} dB")
                
            else:
                print("   ⚠️  降噪功能不可用，跳过实际降噪测试")
                
        except Exception as e:
            print(f"   ❌ 启用状态测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 测试3: 不同采样率
        print("\n🧪 测试3: 不同采样率处理")
        try:
            # 创建48kHz音频（DeepFilterNet的原生采样率）
            sample_rate_48k = 48000
            samples_48k = int(sample_rate_48k * duration)
            t_48k = np.linspace(0, duration, samples_48k, False)
            signal_48k = np.sin(2 * np.pi * 440 * t_48k)
            noise_48k = np.random.normal(0, 0.1, samples_48k)
            noisy_audio_48k = (signal_48k + noise_48k).astype(np.float32)
            
            denoiser = AudioDenoiser(enable=True)
            if denoiser.is_available():
                result_48k = denoiser.denoise_audio(noisy_audio_48k, sample_rate_48k)
                print(f"   ✅ 48kHz音频处理成功")
                print(f"   输入: {noisy_audio_48k.shape}, 48000Hz")
                print(f"   输出: {result_48k.shape}, 48000Hz")
            else:
                print("   ⚠️  降噪功能不可用，跳过48kHz测试")
                
        except Exception as e:
            print(f"   ❌ 48kHz测试失败: {e}")
        
        # 测试4: 边界情况
        print("\n🧪 测试4: 边界情况")
        try:
            denoiser = AudioDenoiser(enable=True)
            
            # 测试很短的音频
            short_audio = np.random.randn(1000).astype(np.float32)
            result_short = denoiser.denoise_audio(short_audio, 16000)
            print(f"   ✅ 短音频处理: {short_audio.shape} -> {result_short.shape}")
            
            # 测试静音音频
            silent_audio = np.zeros(16000, dtype=np.float32)
            result_silent = denoiser.denoise_audio(silent_audio, 16000)
            print(f"   ✅ 静音音频处理: {silent_audio.shape} -> {result_silent.shape}")
            
        except Exception as e:
            print(f"   ❌ 边界情况测试失败: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 DeepFilterNet降噪修复测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_denoiser_fix()
    sys.exit(0 if success else 1)
