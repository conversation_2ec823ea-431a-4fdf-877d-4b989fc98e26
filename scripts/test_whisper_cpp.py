#!/usr/bin/env python3
"""
测试whisper.cpp转录器功能
验证安装、GPU支持和转录功能
"""

import sys
import time
import json
from pathlib import Path

def test_whisper_cpp_import():
    """测试whisper.cpp CLI工具"""
    print("=" * 60)
    print("测试1: whisper.cpp CLI工具检查")
    print("=" * 60)

    try:
        from pathlib import Path

        # 检查whisper-cli可执行文件
        possible_paths = [
            Path("bin/whisper-cli"),
            Path("whisper.cpp/build/bin/whisper-cli"),
            Path("whisper.cpp/build/whisper-cli"),
        ]

        whisper_cli_found = False
        for path in possible_paths:
            if path.exists():
                print(f"✓ 找到whisper-cli: {path}")
                whisper_cli_found = True
                break

        if not whisper_cli_found:
            print("✗ whisper-cli未找到")
            return False

        # 检查模型文件
        model_path = Path("whisper.cpp/models/ggml-large-v3-turbo.bin")
        if model_path.exists():
            print(f"✓ 找到模型文件: {model_path}")
        else:
            print("✗ 模型文件未找到")
            return False

        return True
    except Exception as e:
        print(f"✗ whisper.cpp检查失败: {e}")
        return False

def test_gpu_support():
    """测试GPU支持"""
    print("\n" + "=" * 60)
    print("测试2: GPU支持检查")
    print("=" * 60)

    try:
        import subprocess

        # 使用nvidia-smi检查GPU
        result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total', '--format=csv,noheader,nounits'],
                              capture_output=True, text=True)

        if result.returncode == 0:
            gpu_info = result.stdout.strip().split('\n')
            print(f"✓ 检测到 {len(gpu_info)} 个GPU:")
            for i, info in enumerate(gpu_info):
                name, memory = info.split(', ')
                print(f"  GPU {i}: {name} ({int(memory)/1024:.1f}GB)")
            return True
        else:
            print("✗ 未检测到NVIDIA GPU")
            return False

    except FileNotFoundError:
        print("✗ nvidia-smi未找到，无法检测GPU")
        return False
    except Exception as e:
        print(f"✗ GPU检查失败: {e}")
        return False

def test_whisper_cpp_transcriber():
    """测试whisper.cpp转录器"""
    print("\n" + "=" * 60)
    print("测试3: whisper.cpp转录器初始化")
    print("=" * 60)

    try:
        # 添加项目根目录到Python路径
        import sys
        from pathlib import Path
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))

        # 导入转录器
        from audio_processor.transcribing.whisper_cpp_transcriber import WhisperCppTranscriber

        print("✓ WhisperCppTranscriber导入成功")

        # 创建转录器实例（使用whisper_cpp配置）
        print("正在初始化转录器...")
        transcriber = WhisperCppTranscriber("whisper_cpp")

        print("✓ 转录器初始化成功")
        print(f"设备: {transcriber.device}")

        return transcriber
    except Exception as e:
        print(f"✗ 转录器初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_audio_transcription(transcriber):
    """测试音频转录功能"""
    print("\n" + "=" * 60)
    print("测试4: 音频转录功能")
    print("=" * 60)
    
    # 查找测试音频文件
    test_audio_files = [
        "data/samples/short_test_audio.wav",
        "data/samples/过量（二）.mp3"
    ]
    
    test_file = None
    for audio_file in test_audio_files:
        if Path(audio_file).exists():
            test_file = audio_file
            break
    
    if not test_file:
        print("✗ 未找到测试音频文件")
        print("请确保以下文件之一存在:")
        for f in test_audio_files:
            print(f"  - {f}")
        return False
    
    print(f"使用测试文件: {test_file}")
    
    try:
        # 执行转录
        print("开始转录...")
        start_time = time.time()
        
        result = transcriber.transcribe(
            test_file,
            language="ar",  # 阿拉伯语
            word_timestamps=True
        )
        
        processing_time = time.time() - start_time
        
        print("✓ 转录完成")
        print(f"处理时间: {processing_time:.2f}秒")
        print(f"转录文本: {result.get('text', '')[:100]}...")
        
        # 检查词级时间戳
        segments = result.get('segments', [])
        if segments:
            print(f"段数量: {len(segments)}")
            
            # 检查第一个段是否有词级时间戳
            first_segment = segments[0]
            if 'words' in first_segment:
                words_count = len(first_segment['words'])
                print(f"✓ 词级时间戳可用 (第一段包含 {words_count} 个词)")
            else:
                print("⚠ 词级时间戳不可用")
        
        # 保存测试结果
        output_path = "output/whisper_cpp_test_result.json"
        Path("output").mkdir(exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"结果已保存到: {output_path}")
        return True
        
    except Exception as e:
        print(f"✗ 转录失败: {e}")
        return False

def test_gpu_memory_usage(transcriber):
    """测试GPU内存使用情况"""
    print("\n" + "=" * 60)
    print("测试5: GPU内存使用情况")
    print("=" * 60)
    
    if transcriber.device != "cuda":
        print("跳过GPU内存测试（未使用CUDA）")
        return True
    
    try:
        # 获取内存信息
        memory_info = transcriber.device_manager.get_memory_info()
        
        if memory_info:
            print(f"GPU内存总量: {memory_info['total']:.1f}GB")
            print(f"已分配内存: {memory_info['allocated']:.1f}GB")
            print(f"已保留内存: {memory_info['reserved']:.1f}GB")
            print(f"可用内存: {memory_info['free']:.1f}GB")
            
            # 检查内存使用率
            usage_rate = memory_info['allocated'] / memory_info['total'] * 100
            print(f"内存使用率: {usage_rate:.1f}%")
            
            if usage_rate > 90:
                print("⚠ GPU内存使用率较高")
            else:
                print("✓ GPU内存使用正常")
        
        return True
    except Exception as e:
        print(f"✗ GPU内存检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Whisper.cpp 转录器测试")
    print("=" * 60)
    
    # 测试结果统计
    tests_passed = 0
    total_tests = 5
    
    # 测试1: 导入检查
    if test_whisper_cpp_import():
        tests_passed += 1
    
    # 测试2: GPU支持
    if test_gpu_support():
        tests_passed += 1
    
    # 测试3: 转录器初始化
    transcriber = test_whisper_cpp_transcriber()
    if transcriber:
        tests_passed += 1
        
        # 测试4: 音频转录
        if test_audio_transcription(transcriber):
            tests_passed += 1
        
        # 测试5: GPU内存
        if test_gpu_memory_usage(transcriber):
            tests_passed += 1
        
        # 清理
        try:
            transcriber.unload_model()
        except:
            pass
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果摘要")
    print("=" * 60)
    print(f"通过测试: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✓ 所有测试通过！whisper.cpp转录器工作正常")
        return 0
    else:
        print("✗ 部分测试失败，请检查配置和安装")
        return 1

if __name__ == "__main__":
    sys.exit(main())
