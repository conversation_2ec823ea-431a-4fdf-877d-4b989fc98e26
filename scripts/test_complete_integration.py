#!/usr/bin/env python3
"""
完整集成测试：使用实际音频文件测试降噪、预处理和Whisper的完整流程
"""

import sys
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_audio_file_processing(audio_file_path: str):
    """测试完整的音频文件处理流程"""
    print(f"🎵 测试音频文件: {audio_file_path}")
    print("=" * 80)

    # 检查文件是否存在
    if not Path(audio_file_path).exists():
        print(f"❌ 音频文件不存在: {audio_file_path}")
        return False

    try:
        # 1. 测试音频文件基本信息
        print("📊 步骤1: 获取音频文件信息")
        import librosa

        # 获取音频信息（不加载数据）
        duration = librosa.get_duration(path=audio_file_path)
        print(f"   文件路径: {audio_file_path}")
        print(f"   音频时长: {duration:.2f}秒")

        # 加载音频数据
        audio_data, sample_rate = librosa.load(audio_file_path, sr=None)
        print(f"   原始采样率: {sample_rate}Hz")
        print(f"   音频形状: {audio_data.shape}")
        print(f"   数据类型: {audio_data.dtype}")
        print(f"   音频范围: [{audio_data.min():.3f}, {audio_data.max():.3f}]")

        # 2. 测试单独的降噪功能
        print("\n🔧 步骤2: 测试降噪功能")
        from audio_processor.preprocessing.denoiser import AudioDenoiser

        # 测试禁用状态
        denoiser_disabled = AudioDenoiser(enable=False)
        result_disabled = denoiser_disabled.denoise_audio(audio_data, sample_rate)
        print(f"   禁用降噪: {audio_data.shape} -> {result_disabled.shape}")
        print(f"   数据相同: {np.array_equal(audio_data, result_disabled)}")

        # 测试启用状态
        denoiser_enabled = AudioDenoiser(enable=True)
        if denoiser_enabled.is_available():
            print("   ✅ DeepFilterNet可用，测试降噪...")
            start_time = time.time()
            result_enabled = denoiser_enabled.denoise_audio(audio_data, sample_rate)
            denoise_time = time.time() - start_time

            print(f"   启用降噪: {audio_data.shape} -> {result_enabled.shape}")
            print(f"   降噪耗时: {denoise_time:.2f}秒")
            print(f"   数据相同: {np.array_equal(audio_data, result_enabled)}")

            # 计算降噪效果
            if not np.array_equal(audio_data, result_enabled):
                rms_before = np.sqrt(np.mean(audio_data**2))
                rms_after = np.sqrt(np.mean(result_enabled**2))
                print(f"   RMS变化: {rms_before:.4f} -> {rms_after:.4f}")
        else:
            print("   ⚠️  DeepFilterNet不可用，跳过降噪测试")

        # 3. 测试预处理功能
        print("\n🔄 步骤3: 测试音频预处理")
        from audio_processor.preprocessing.processor import AudioPreprocessor

        # 创建预处理器
        preprocessor = AudioPreprocessor()
        print(f"   预处理器状态: {'启用' if preprocessor.enabled else '禁用'}")

        if preprocessor.enabled:
            print("   开始预处理...")
            start_time = time.time()
            preprocess_result = preprocessor.preprocess_audio(audio_file_path)
            preprocess_time = time.time() - start_time

            print(f"   预处理耗时: {preprocess_time:.2f}秒")
            print(f"   检测到语音段: {len(preprocess_result.segments)}个")

            # 显示语音段信息
            for i, segment in enumerate(preprocess_result.segments[:5]):  # 只显示前5个
                print(f"     段{i}: {segment.start:.2f}s - {segment.end:.2f}s "
                      f"(时长: {segment.end - segment.start:.2f}s)")

            if len(preprocess_result.segments) > 5:
                print(f"     ... 还有 {len(preprocess_result.segments) - 5} 个段")
        else:
            print("   预处理已禁用")
            preprocess_result = None

        # 4. 测试Whisper转录
        print("\n🎤 步骤4: 测试Whisper转录")
        from audio_processor.transcribing.transcriber import WhisperTranscriber

        # 创建转录器
        transcriber = WhisperTranscriber()
        model_name = transcriber.get_config_value("model.name", "openai/whisper-large-v3-turbo")
        print(f"   模型: {model_name}")

        print("   开始转录...")
        start_time = time.time()
        result = transcriber.transcribe(audio_file_path)
        transcribe_time = time.time() - start_time

        print(f"   转录耗时: {transcribe_time:.2f}秒")
        print(f"   转录结果类型: {type(result)}")

        if isinstance(result, dict):
            text = result.get('text', '')
            segments = result.get('segments', [])

            print(f"   转录文本长度: {len(text)}字符")
            print(f"   转录段数: {len(segments)}个")

            # 显示转录文本（前200字符）
            if text:
                preview_text = text[:200] + "..." if len(text) > 200 else text
                print(f"   转录文本预览: {preview_text}")
            else:
                print("   ⚠️  未检测到转录文本")

            # 显示转录段信息
            for i, segment in enumerate(segments[:3]):  # 只显示前3个段
                seg_text = segment.get('text', '').strip()
                seg_start = segment.get('start', 0)
                seg_end = segment.get('end', 0)
                print(f"     段{i}: {seg_start:.2f}s-{seg_end:.2f}s \"{seg_text[:50]}...\"")

        # 5. 测试完整集成流程
        print("\n🔗 步骤5: 测试完整集成流程")
        print("   使用预处理 + Whisper转录...")

        # 重新创建转录器以确保干净状态
        transcriber_with_preprocessing = WhisperTranscriber()

        start_time = time.time()
        integrated_result = transcriber_with_preprocessing.transcribe(audio_file_path)
        integrated_time = time.time() - start_time

        print(f"   集成流程耗时: {integrated_time:.2f}秒")

        if isinstance(integrated_result, dict):
            integrated_text = integrated_result.get('text', '')
            integrated_segments = integrated_result.get('segments', [])

            print(f"   集成转录文本长度: {len(integrated_text)}字符")
            print(f"   集成转录段数: {len(integrated_segments)}个")

            # 比较结果
            if isinstance(result, dict):
                original_text = result.get('text', '')
                if original_text and integrated_text:
                    similarity = len(set(original_text.split()) & set(integrated_text.split())) / max(len(original_text.split()), len(integrated_text.split()))
                    print(f"   文本相似度: {similarity:.2%}")

        print("\n" + "=" * 80)
        print("🎉 完整集成测试完成！")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 完整集成流程测试")
    print("=" * 80)

    # 测试音频文件路径
    audio_files = [
        "data/samples/过量（二）.mp3"
    ]

    success_count = 0
    total_count = len(audio_files)

    for audio_file in audio_files:
        print(f"\n📁 测试文件 {success_count + 1}/{total_count}")
        if test_audio_file_processing(audio_file):
            success_count += 1
        print()

    print("=" * 80)
    print("🎯 测试结果总结:")
    print(f"   成功: {success_count}/{total_count} 个文件")
    print(f"   成功率: {success_count/total_count*100:.1f}%")

    if success_count == total_count:
        print("🎉 所有测试通过！完整集成流程正常工作")
    else:
        print("⚠️  部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
