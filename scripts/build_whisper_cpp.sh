#!/bin/bash

# Whisper.cpp 编译脚本 - 针对RTX5090优化
# 支持sm_120架构和所有性能优化选项

set -e

echo "=== Whisper.cpp 编译脚本 (RTX5090 sm_120优化) ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
WHISPER_CPP_DIR="whisper.cpp"
WHISPER_CPP_REPO="https://github.com/ggerganov/whisper.cpp.git"
BUILD_DIR="build"

# 检查环境
check_environment() {
    log_info "检查编译环境..."

    # 检查CUDA
    if ! command -v nvcc &> /dev/null; then
        log_error "NVCC not found. Please install CUDA toolkit."
        exit 1
    fi

    # 检查cmake
    if ! command -v cmake &> /dev/null; then
        log_error "CMake not found. Please install cmake."
        exit 1
    fi

    # 检查Python开发环境
    if ! python3 -c "import distutils.util" &> /dev/null; then
        log_error "Python development environment not found."
        exit 1
    fi

    log_success "环境检查通过"
}

# 克隆或更新whisper.cpp
setup_source() {
    log_info "设置whisper.cpp源码..."

    if [ -d "$WHISPER_CPP_DIR" ]; then
        log_warning "whisper.cpp目录已存在，将更新到最新版本..."
        cd "$WHISPER_CPP_DIR"
        git pull origin master
        cd ..
    else
        log_info "克隆whisper.cpp仓库..."
        git clone "$WHISPER_CPP_REPO" "$WHISPER_CPP_DIR"
    fi

    log_success "源码准备完成"
}

# 配置编译选项
configure_build() {
    log_info "配置编译选项..."

    cd "$WHISPER_CPP_DIR"

    # 清理之前的构建
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"

    log_info "CMake配置 - RTX5090 sm_120优化..."

    # RTX5090优化的CMake配置 (Blackwell架构使用sm_120)
    cmake .. \
        -DCMAKE_BUILD_TYPE=Release \
        -DGGML_CUDA=ON \
        -DCMAKE_CUDA_ARCHITECTURES="70;75;80;86;90;120" \
        -DGGML_CUDA_F16=ON \
        -DGGML_NATIVE=ON \
        -DGGML_K_QUANTS=ON \
        -DGGML_QKK_64=ON \
        -DGGML_CUDA_FORCE_DMMV=ON \
        -DGGML_CUDA_FORCE_MMQ=ON \
        -DGGML_CUDA_FA_ALL_QUANTS=ON \
        -DCMAKE_CUDA_FLAGS="-arch=sm_120 -gencode=arch=compute_70,code=sm_70 -gencode=arch=compute_75,code=sm_75 -gencode=arch=compute_80,code=sm_80 -gencode=arch=compute_86,code=sm_86 -gencode=arch=compute_90,code=sm_90 -gencode=arch=compute_120,code=sm_120" \
        -DCMAKE_CXX_FLAGS="-O3 -march=native -mtune=native" \
        -DCMAKE_C_FLAGS="-O3 -march=native -mtune=native"

    log_success "CMake配置完成"
}

# 编译
build_whisper_cpp() {
    log_info "开始编译whisper.cpp..."

    # 获取CPU核心数用于并行编译
    NPROC=$(nproc)
    log_info "使用 $NPROC 个CPU核心进行并行编译..."

    # 编译
    make -j"$NPROC"

    log_success "编译完成!"
}

# 验证编译结果
verify_build() {
    log_info "验证编译结果..."

    # 检查关键可执行文件
    EXECUTABLES=("whisper-cli" "whisper-bench" "whisper-server")

    for exe in "${EXECUTABLES[@]}"; do
        if [ -f "./$exe" ]; then
            log_success "✓ $exe 编译成功"
        else
            log_error "✗ $exe 编译失败"
            exit 1
        fi
    done

    # 测试CUDA支持
    log_info "测试CUDA支持..."
    if ./whisper-cli --help | grep -q "gpu"; then
        log_success "✓ CUDA支持已启用"
    else
        log_warning "⚠ CUDA支持可能未正确启用"
    fi

    log_success "编译验证完成!"
}

# 安装Python绑定
install_python_bindings() {
    log_info "安装Python绑定..."

    cd ..  # 回到whisper.cpp根目录

    # 设置环境变量以启用CUDA支持
    export CMAKE_ARGS="-DGGML_CUDA=ON -DCMAKE_CUDA_ARCHITECTURES=120"
    export FORCE_CMAKE=1

    # 安装Python绑定
    pip install -e .

    log_success "Python绑定安装完成!"
}

# 创建符号链接
create_links() {
    log_info "创建可执行文件链接..."

    cd ../..  # 回到项目根目录

    # 创建bin目录
    mkdir -p bin

    # 创建符号链接
    ln -sf "../$WHISPER_CPP_DIR/$BUILD_DIR/bin/whisper-cli" bin/whisper-cli
    ln -sf "../$WHISPER_CPP_DIR/$BUILD_DIR/bin/whisper-bench" bin/whisper-bench
    ln -sf "../$WHISPER_CPP_DIR/$BUILD_DIR/bin/whisper-server" bin/whisper-server

    log_success "符号链接创建完成"
}

# 显示编译信息
show_build_info() {
    log_info "编译信息摘要:"
    echo "=================================="
    echo "编译目录: $WHISPER_CPP_DIR/$BUILD_DIR"
    echo "CUDA架构: 70;75;80;86;90;120 (包含RTX5090 sm_120)"
    echo "优化选项: CUDA, F16, Native, K-Quants, QKK_64"
    echo "可执行文件位置: ./bin/"
    echo "Python绑定: 已安装到当前环境"
    echo "=================================="
    echo ""
    log_info "接下来可以运行测试脚本:"
    echo "python -c \"import whisper_cpp; print('whisper.cpp Python绑定安装成功')\""
}

# 主函数
main() {
    log_info "开始编译whisper.cpp..."

    check_environment
    setup_source
    configure_build
    build_whisper_cpp
    verify_build
    install_python_bindings
    create_links
    show_build_info

    log_success "whisper.cpp编译完成!"
}

# 执行主函数
main "$@"
