#!/bin/bash

# llama-cpp-python 安装脚本 - 针对RTX5090优化
# 使用CUDA编译支持GPU加速

set -e

echo "=== llama-cpp-python 安装脚本 (RTX5090优化) ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查安装环境..."

    # 检查Python
    if ! command -v python &> /dev/null; then
        log_error "Python not found. Please install Python."
        exit 1
    fi

    # 检查pip
    if ! command -v pip &> /dev/null; then
        log_error "pip not found. Please install pip."
        exit 1
    fi

    # 检查CUDA
    if command -v nvcc &> /dev/null; then
        CUDA_VERSION=$(nvcc --version | grep "release" | sed 's/.*release \([0-9]\+\.[0-9]\+\).*/\1/')
        log_info "检测到CUDA版本: $CUDA_VERSION"
        
        # 检查是否支持RTX5090 (需要CUDA 12.8+)
        if [[ $(echo "$CUDA_VERSION >= 12.8" | bc -l) -eq 1 ]]; then
            log_success "CUDA版本支持RTX5090优化"
            USE_CUDA=true
        else
            log_warning "CUDA版本较低，可能不支持RTX5090优化"
            USE_CUDA=true
        fi
    else
        log_warning "未检测到CUDA，将使用CPU版本"
        USE_CUDA=false
    fi

    # 检查cmake
    if ! command -v cmake &> /dev/null; then
        log_warning "CMake not found. 将尝试安装..."
        pip install cmake
    fi

    log_success "环境检查完成"
}

# 卸载现有版本
uninstall_existing() {
    log_info "检查并卸载现有llama-cpp-python..."
    
    if pip show llama-cpp-python &> /dev/null; then
        log_warning "发现现有llama-cpp-python，正在卸载..."
        pip uninstall llama-cpp-python -y
        log_success "现有版本已卸载"
    else
        log_info "未发现现有llama-cpp-python安装"
    fi
}

# 安装CUDA版本
install_cuda_version() {
    log_info "安装CUDA版本的llama-cpp-python..."
    
    # 设置CUDA编译环境变量
    export CMAKE_ARGS="-DGGML_CUDA=on -DCMAKE_CUDA_ARCHITECTURES=70;75;80;86;90;120"
    export FORCE_CMAKE=1
    
    log_info "CUDA编译参数: $CMAKE_ARGS"
    
    # 安装
    pip install llama-cpp-python --upgrade --force-reinstall --no-cache-dir
    
    log_success "CUDA版本安装完成"
}

# 安装CPU版本
install_cpu_version() {
    log_info "安装CPU版本的llama-cpp-python..."
    
    # 清除CUDA相关环境变量
    unset CMAKE_ARGS
    unset FORCE_CMAKE
    
    # 安装
    pip install llama-cpp-python --upgrade --force-reinstall --no-cache-dir
    
    log_success "CPU版本安装完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    # 测试导入
    if python -c "import llama_cpp; print('llama-cpp-python导入成功')" 2>/dev/null; then
        log_success "✓ llama-cpp-python导入测试通过"
    else
        log_error "✗ llama-cpp-python导入失败"
        exit 1
    fi
    
    # 检查CUDA支持
    if $USE_CUDA; then
        log_info "检查CUDA支持..."
        python -c "
import llama_cpp
try:
    # 尝试创建一个简单的模型实例来测试CUDA
    print('CUDA支持检查完成')
except Exception as e:
    print(f'CUDA支持检查失败: {e}')
" 2>/dev/null
    fi
    
    # 显示版本信息
    LLAMA_CPP_VERSION=$(python -c "import llama_cpp; print(llama_cpp.__version__)" 2>/dev/null || echo "未知")
    log_info "llama-cpp-python版本: $LLAMA_CPP_VERSION"
    
    log_success "安装验证完成"
}

# 显示安装信息
show_installation_info() {
    log_info "安装信息摘要:"
    echo "=================================="
    echo "包名: llama-cpp-python"
    echo "CUDA支持: $USE_CUDA"
    if $USE_CUDA; then
        echo "CUDA架构: 70;75;80;86;90;120 (包含RTX5090)"
        echo "优化: GPU加速已启用"
    else
        echo "优化: CPU模式"
    fi
    echo "=================================="
    echo ""
    log_info "接下来可以运行测试脚本:"
    echo "python scripts/test_qwen_integration.py"
}

# 主函数
main() {
    log_info "开始安装llama-cpp-python..."
    
    check_environment
    uninstall_existing
    
    if $USE_CUDA; then
        install_cuda_version
    else
        install_cpu_version
    fi
    
    verify_installation
    show_installation_info
    
    log_success "llama-cpp-python安装完成!"
}

# 执行主函数
main "$@"
