#!/usr/bin/env python3
"""
测试transformers版本兼容性脚本
用于找到camel-tools和Qwen3模型都能工作的transformers版本
"""

import subprocess
import sys
import json
from pathlib import Path
from typing import List, Dict, Any

def test_transformers_version(version: str) -> Dict[str, Any]:
    """
    测试特定transformers版本的兼容性
    
    Args:
        version: transformers版本号
        
    Returns:
        测试结果字典
    """
    print(f"测试transformers版本: {version}")
    
    result = {
        "version": version,
        "camel_tools_compatible": False,
        "qwen_compatible": False,
        "installation_success": False,
        "errors": []
    }
    
    try:
        # 尝试安装指定版本的transformers
        print(f"  安装transformers=={version}...")
        install_result = subprocess.run(
            [sys.executable, "-m", "pip", "install", f"transformers=={version}"],
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if install_result.returncode != 0:
            result["errors"].append(f"安装失败: {install_result.stderr}")
            return result
        
        result["installation_success"] = True
        
        # 测试camel-tools兼容性
        print("  测试camel-tools兼容性...")
        camel_test = subprocess.run(
            [sys.executable, "-c", "import camel_tools; print('camel-tools导入成功')"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if camel_test.returncode == 0:
            result["camel_tools_compatible"] = True
        else:
            result["errors"].append(f"camel-tools测试失败: {camel_test.stderr}")
        
        # 测试Qwen模型兼容性
        print("  测试Qwen模型兼容性...")
        qwen_test_code = '''
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    # 尝试加载一个小的模型来测试兼容性
    tokenizer = AutoTokenizer.from_pretrained("microsoft/DialoGPT-small")
    print("Qwen模型兼容性测试通过")
except Exception as e:
    print(f"Qwen模型兼容性测试失败: {e}")
    raise
'''
        
        qwen_test = subprocess.run(
            [sys.executable, "-c", qwen_test_code],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if qwen_test.returncode == 0:
            result["qwen_compatible"] = True
        else:
            result["errors"].append(f"Qwen测试失败: {qwen_test.stderr}")
            
    except subprocess.TimeoutExpired:
        result["errors"].append("测试超时")
    except Exception as e:
        result["errors"].append(f"测试异常: {str(e)}")
    
    return result

def main():
    """主函数"""
    # 要测试的transformers版本列表
    test_versions = [
        "4.43.0",  # camel-tools支持的最高版本
        "4.42.0",
        "4.41.0", 
        "4.40.0",
        "4.35.0",
        "4.30.0",
        "4.25.0",
        "4.20.0",
        "4.15.0",
        "4.10.0",
        "4.5.0"   # Qwen3要求的最低版本
    ]
    
    results = []
    
    print("开始测试transformers版本兼容性...")
    print("=" * 50)
    
    for version in test_versions:
        try:
            result = test_transformers_version(version)
            results.append(result)
            
            # 打印结果摘要
            status = "✓" if result["camel_tools_compatible"] and result["qwen_compatible"] else "✗"
            print(f"{status} {version}: camel-tools={result['camel_tools_compatible']}, qwen={result['qwen_compatible']}")
            
            if result["errors"]:
                for error in result["errors"]:
                    print(f"    错误: {error}")
            
        except KeyboardInterrupt:
            print("\n测试被用户中断")
            break
        except Exception as e:
            print(f"测试版本{version}时发生异常: {e}")
            continue
        
        print("-" * 30)
    
    # 保存详细结果
    results_file = Path("compatibility_test_results.json")
    with open(results_file, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细测试结果已保存到: {results_file}")
    
    # 找到兼容的版本
    compatible_versions = [
        r["version"] for r in results 
        if r["camel_tools_compatible"] and r["qwen_compatible"]
    ]
    
    if compatible_versions:
        print(f"\n找到兼容版本: {compatible_versions}")
        print(f"推荐使用: {compatible_versions[0]}")
    else:
        print("\n未找到完全兼容的版本，建议使用虚拟环境隔离方案")

if __name__ == "__main__":
    main()
