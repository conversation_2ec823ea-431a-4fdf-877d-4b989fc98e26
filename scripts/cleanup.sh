#!/bin/bash

# 项目清理脚本
# 清理不必要的文件和缓存

set -e

echo "=== Audio Processor 项目清理脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 清理Python缓存文件
cleanup_python_cache() {
    log_info "清理Python缓存文件..."
    
    # 清理 __pycache__ 目录
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    
    # 清理 .pyc 文件
    find . -name "*.pyc" -delete 2>/dev/null || true
    
    # 清理 .pyo 文件
    find . -name "*.pyo" -delete 2>/dev/null || true
    
    log_success "Python缓存文件清理完成"
}

# 清理日志文件
cleanup_logs() {
    log_info "清理日志文件..."
    
    # 清理日志目录中的旧日志
    if [ -d "logs" ]; then
        find logs -name "*.log" -mtime +7 -delete 2>/dev/null || true
        find logs -name "*.log.*" -delete 2>/dev/null || true
    fi
    
    log_success "日志文件清理完成"
}

# 清理临时文件
cleanup_temp_files() {
    log_info "清理临时文件..."
    
    # 清理临时目录
    rm -rf temp/ tmp/ .tmp/ 2>/dev/null || true
    
    # 清理输出目录中的临时文件
    if [ -d "output" ]; then
        find output -name "*.tmp" -delete 2>/dev/null || true
        find output -name "*.temp" -delete 2>/dev/null || true
    fi
    
    # 清理备份文件
    find . -name "*.bak" -delete 2>/dev/null || true
    find . -name "*.backup" -delete 2>/dev/null || true
    find . -name "*.old" -delete 2>/dev/null || true
    find . -name "*~" -delete 2>/dev/null || true
    
    log_success "临时文件清理完成"
}

# 清理IDE文件
cleanup_ide_files() {
    log_info "清理IDE配置文件..."
    
    # 清理VSCode配置（如果不需要）
    if [ "$1" = "--include-ide" ]; then
        rm -rf .vscode/ 2>/dev/null || true
        log_warning "已删除VSCode配置文件"
    fi
    
    # 清理PyCharm配置
    rm -rf .idea/ 2>/dev/null || true
    
    # 清理Vim临时文件
    find . -name "*.swp" -delete 2>/dev/null || true
    find . -name "*.swo" -delete 2>/dev/null || true
    
    log_success "IDE文件清理完成"
}

# 清理系统文件
cleanup_system_files() {
    log_info "清理系统文件..."
    
    # 清理macOS文件
    find . -name ".DS_Store" -delete 2>/dev/null || true
    find . -name "._*" -delete 2>/dev/null || true
    
    # 清理Windows文件
    find . -name "Thumbs.db" -delete 2>/dev/null || true
    find . -name "Desktop.ini" -delete 2>/dev/null || true
    
    log_success "系统文件清理完成"
}

# 清理测试输出
cleanup_test_outputs() {
    log_info "清理测试输出文件..."
    
    # 清理测试结果
    rm -rf test_output/ test_results/ 2>/dev/null || true
    
    # 清理基准测试结果
    find . -name "benchmark_results*.json" -delete 2>/dev/null || true
    find . -name "performance_*.json" -delete 2>/dev/null || true
    
    # 清理覆盖率报告
    rm -rf htmlcov/ .coverage coverage.xml 2>/dev/null || true
    
    log_success "测试输出清理完成"
}

# 显示磁盘使用情况
show_disk_usage() {
    log_info "显示磁盘使用情况..."
    
    echo "项目目录大小:"
    du -sh . 2>/dev/null || echo "无法获取目录大小"
    
    echo ""
    echo "大文件列表 (>10MB):"
    find . -type f -size +10M -exec ls -lh {} \; 2>/dev/null | head -10 || echo "没有找到大文件"
}

# 主函数
main() {
    echo "开始清理项目文件..."
    echo ""
    
    # 检查是否在项目根目录
    if [ ! -f "README.md" ] || [ ! -d "audio_processor" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行清理
    cleanup_python_cache
    cleanup_logs
    cleanup_temp_files
    cleanup_ide_files "$1"
    cleanup_system_files
    cleanup_test_outputs
    
    echo ""
    show_disk_usage
    
    echo ""
    log_success "项目清理完成！"
    
    # 提示用户
    echo ""
    log_info "清理完成后的建议:"
    echo "1. 运行测试确保项目正常: python scripts/test_qwen_integration.py"
    echo "2. 检查 .gitignore 文件确保不必要的文件不会被提交"
    echo "3. 如果需要，重新生成虚拟环境: conda create -n audio_processor_env python=3.13"
    echo ""
    log_warning "注意: 如果删除了重要文件，请从备份或git历史中恢复"
}

# 显示帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --include-ide    同时清理IDE配置文件 (如 .vscode/)"
    echo "  --help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                # 基本清理"
    echo "  $0 --include-ide  # 包含IDE文件的清理"
}

# 解析命令行参数
case "$1" in
    --help)
        show_help
        exit 0
        ;;
    *)
        main "$1"
        ;;
esac
