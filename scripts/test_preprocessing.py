#!/usr/bin/env python3
"""
音频预处理功能测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from audio_processor.preprocessing.processor import AudioPreprocessor
from audio_processor.preprocessing.audio_normalizer import AudioNormalizer
from audio_processor.preprocessing.window_processor import WindowProcessor
from audio_processor.preprocessing.vad_detector import VADDetector
from audio_processor.preprocessing.segment_merger import SegmentMerger

import numpy as np
import librosa
from loguru import logger


def test_audio_normalizer():
    """测试音频标准化器"""
    print("\n=== 测试音频标准化器 ===")

    try:
        normalizer = AudioNormalizer(
            target_sample_rate=16000,
            target_channels=1,
            normalize_audio=True,
            normalization_method="peak"
        )

        # 创建测试音频数据
        duration = 5.0  # 5秒
        sample_rate = 44100
        t = np.linspace(0, duration, int(sample_rate * duration))
        audio_data = 0.5 * np.sin(2 * np.pi * 440 * t)  # 440Hz正弦波

        # 标准化音频
        normalized_audio, final_sr = normalizer.normalize_audio_data(audio_data, sample_rate)

        print(f"原始音频: {audio_data.shape}, {sample_rate}Hz")
        print(f"标准化后: {normalized_audio.shape}, {final_sr}Hz")
        print(f"音频范围: [{normalized_audio.min():.3f}, {normalized_audio.max():.3f}]")
        print("✅ 音频标准化器测试通过")

    except Exception as e:
        print(f"❌ 音频标准化器测试失败: {e}")


def test_window_processor():
    """测试滑动窗口处理器"""
    print("\n=== 测试滑动窗口处理器 ===")

    try:
        window_processor = WindowProcessor(
            window_duration=10.0,
            hop_duration=5.0
        )

        # 创建测试音频数据（30秒）
        duration = 30.0
        sample_rate = 16000
        audio_data = np.random.randn(int(sample_rate * duration)) * 0.1

        # 创建窗口
        windows = window_processor.create_windows(audio_data, sample_rate)

        print(f"音频时长: {duration}秒")
        print(f"创建窗口数: {len(windows)}")

        for i, window in enumerate(windows[:3]):  # 只显示前3个窗口
            print(f"窗口 {i}: {window.start_time:.1f}s - {window.end_time:.1f}s (时长: {window.duration:.1f}s)")

        print("✅ 滑动窗口处理器测试通过")

    except Exception as e:
        print(f"❌ 滑动窗口处理器测试失败: {e}")


def test_vad_detector():
    """测试VAD检测器"""
    print("\n=== 测试VAD检测器 ===")

    try:
        vad_detector = VADDetector(
            threshold=0.5,
            min_speech_duration=0.25,
            min_silence_duration=0.1
        )

        # 创建测试音频数据（包含语音和静音段）
        sample_rate = 16000
        duration = 10.0

        # 创建简单的语音模拟数据
        t = np.linspace(0, duration, int(sample_rate * duration))
        audio_data = np.zeros_like(t)

        # 添加一些"语音"段（高频内容）
        speech_segments = [(1.0, 3.0), (5.0, 7.5)]
        for start, end in speech_segments:
            start_idx = int(start * sample_rate)
            end_idx = int(end * sample_rate)
            audio_data[start_idx:end_idx] = 0.3 * np.random.randn(end_idx - start_idx)

        # 检测语音段
        segments = vad_detector.detect_speech_segments(audio_data, sample_rate)

        print(f"音频时长: {duration}秒")
        print(f"检测到语音段数: {len(segments)}")

        for i, segment in enumerate(segments):
            print(f"语音段 {i}: {segment.start:.2f}s - {segment.end:.2f}s (时长: {segment.duration:.2f}s)")

        print("✅ VAD检测器测试通过")

    except Exception as e:
        print(f"❌ VAD检测器测试失败: {e}")
        print("注意：VAD检测器需要安装torch和torchaudio")


def test_segment_merger():
    """测试语音段合并器"""
    print("\n=== 测试语音段合并器 ===")

    try:
        from audio_processor.preprocessing.vad_detector import SpeechSegment

        merger = SegmentMerger(
            overlap_threshold=0.5,
            min_segment_duration=0.5,
            max_segment_duration=30.0
        )

        # 创建测试语音段（包含重叠）
        segments = [
            SpeechSegment(1.0, 3.0),
            SpeechSegment(2.5, 4.5),  # 与第一个重叠
            SpeechSegment(6.0, 8.0),
            SpeechSegment(7.8, 9.5),  # 与第三个重叠
            SpeechSegment(12.0, 13.0)
        ]

        print("原始语音段:")
        for i, seg in enumerate(segments):
            print(f"  段 {i}: {seg.start:.1f}s - {seg.end:.1f}s")

        # 合并语音段
        merged_segments = merger.merge_segments(segments)

        print("\n合并后语音段:")
        for seg in merged_segments:
            print(f"  段 {seg.segment_id}: {seg.start:.1f}s - {seg.end:.1f}s (时长: {seg.duration:.1f}s)")

        print("✅ 语音段合并器测试通过")

    except Exception as e:
        print(f"❌ 语音段合并器测试失败: {e}")


def test_full_preprocessing():
    """测试完整预处理流程"""
    print("\n=== 测试完整预处理流程 ===")

    try:
        # 注意：这个测试需要配置文件中启用预处理
        preprocessor = AudioPreprocessor("preprocessing")

        if not preprocessor.is_enabled():
            print("⚠️  预处理功能未启用，请在 configs/preprocessing.yaml 中设置 enable: true")
            return

        print("预处理器组件信息:")
        info = preprocessor.get_component_info()
        for component, details in info.items():
            if isinstance(details, dict):
                print(f"  {component}: {details}")
            else:
                print(f"  {component}: {details}")

        print("✅ 完整预处理流程测试通过")

    except Exception as e:
        print(f"❌ 完整预处理流程测试失败: {e}")


def test_real_audio_integration():
    """测试真实音频文件的完整集成流程"""
    print("\n=== 测试真实音频文件集成流程 ===")

    import os
    from pathlib import Path

    # 查找样本音频文件
    samples_dir = Path("data/samples")
    audio_files = []

    if samples_dir.exists():
        for ext in ['.mp3', '.wav', '.flac', '.m4a', '.ogg']:
            audio_files.extend(samples_dir.glob(f"*{ext}"))

    if not audio_files:
        print("❌ 未找到样本音频文件")
        return

    audio_file = audio_files[0]
    print(f"📁 使用音频文件: {audio_file}")

    try:
        # 1. 测试音频加载
        print("\n🔊 步骤1: 加载音频文件")
        import librosa
        audio_data, sample_rate = librosa.load(str(audio_file), sr=None)
        print(f"   原始音频: {audio_data.shape}, {sample_rate}Hz, 时长: {len(audio_data)/sample_rate:.2f}秒")

        # 2. 测试音频标准化
        print("\n🎛️  步骤2: 音频标准化")
        from audio_processor.preprocessing.audio_normalizer import AudioNormalizer
        normalizer = AudioNormalizer()
        normalized_audio, final_sr = normalizer.normalize_audio_data(audio_data, int(sample_rate))
        print(f"   标准化后: {normalized_audio.shape}, {final_sr}Hz, 时长: {len(normalized_audio)/final_sr:.2f}秒")

        # 3. 测试滑动窗口
        print("\n🪟 步骤3: 滑动窗口处理")
        from audio_processor.preprocessing.window_processor import WindowProcessor
        window_processor = WindowProcessor()
        windows = list(window_processor.create_windows(normalized_audio, final_sr))
        print(f"   创建了 {len(windows)} 个窗口")

        # 4. 测试VAD检测（使用第一个窗口）
        print("\n🎤 步骤4: VAD语音检测")
        if windows:
            try:
                from audio_processor.preprocessing.vad_detector import VADDetector
                vad_detector = VADDetector()
                window_data = windows[0].audio_data
                segments = vad_detector.detect_speech_segments(window_data, final_sr)
                print(f"   检测到 {len(segments)} 个语音段")
                for i, segment in enumerate(segments[:3]):  # 只显示前3个
                    print(f"     段{i+1}: {segment.start:.2f}s - {segment.end:.2f}s")
            except Exception as e:
                print(f"   ⚠️  VAD检测跳过: {e}")

        # 5. 测试降噪（如果可用）
        print("\n🔇 步骤5: 音频降噪")
        try:
            from audio_processor.preprocessing.denoiser import AudioDenoiser
            denoiser = AudioDenoiser(enable=True)
            if denoiser.enable:
                denoised_audio = denoiser.denoise_audio(normalized_audio, final_sr)
                print(f"   降噪完成: {denoised_audio.shape}")
            else:
                print("   ⚠️  降噪功能未启用，使用原始音频")
                denoised_audio = normalized_audio
        except Exception as e:
            print(f"   ⚠️  降噪失败，使用原始音频: {e}")
            denoised_audio = normalized_audio

        # 6. 测试Whisper转录
        print("\n🎯 步骤6: Whisper转录")
        try:
            from audio_processor.transcribing.transcriber import WhisperTranscriber
            whisper_transcriber = WhisperTranscriber("whisper")

            # 使用前30秒进行测试（避免太长）
            test_duration = min(30.0, len(denoised_audio) / final_sr)
            test_samples = int(test_duration * final_sr)
            test_audio = denoised_audio[:test_samples]

            print(f"   转录音频片段: {test_duration:.1f}秒")

            # 保存测试音频到临时文件
            import tempfile
            import soundfile as sf
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name
                sf.write(temp_path, test_audio, final_sr)

                # 执行转录
                result = whisper_transcriber.transcribe(temp_path)

                # 删除临时文件
                import os
                os.unlink(temp_path)

            if result and 'text' in result:
                print(f"   转录结果: {result['text'][:100]}...")
                if 'segments' in result:
                    print(f"   检测到 {len(result['segments'])} 个转录段")
            else:
                print("   ⚠️  转录结果为空")

        except Exception as e:
            print(f"   ❌ Whisper转录失败: {e}")

        print("\n🎉 完整集成流程测试完成！")

    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    print("🧪 音频预处理功能测试")
    print("=" * 50)

    # 运行各个组件测试
    test_audio_normalizer()
    test_window_processor()
    test_vad_detector()
    test_segment_merger()
    test_full_preprocessing()

    # 运行真实音频集成测试
    test_real_audio_integration()

    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("\n💡 提示:")
    print("1. 如果VAD检测器测试失败，请确保安装了torch和torchaudio")
    print("2. 如果要测试完整预处理流程，请在configs/preprocessing.yaml中启用预处理")
    print("3. 降噪功能需要安装DeepFilterNet，否则会使用简化降噪器")


if __name__ == "__main__":
    main()
