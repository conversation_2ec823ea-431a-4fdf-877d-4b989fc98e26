#!/bin/bash

# 音频预处理依赖安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python版本
check_python() {
    log_info "检查Python版本..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装Python 3.8+"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print('.'.join(map(str, sys.version_info[:2])))")
    log_info "Python版本: $python_version"
    
    # 检查版本是否 >= 3.8
    if python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
        log_success "Python版本检查通过"
    else
        log_error "需要Python 3.8或更高版本"
        exit 1
    fi
}

# 安装基础依赖
install_basic_deps() {
    log_info "安装基础音频处理依赖..."
    
    pip install librosa>=0.10.0 soundfile>=0.12.0 scipy>=1.10.0 numpy>=1.24.0
    
    if [ $? -eq 0 ]; then
        log_success "基础音频处理依赖安装完成"
    else
        log_error "基础依赖安装失败"
        exit 1
    fi
}

# 安装PyTorch
install_pytorch() {
    log_info "安装PyTorch和torchaudio..."
    
    # 检查是否有CUDA
    if command -v nvidia-smi &> /dev/null; then
        log_info "检测到NVIDIA GPU，安装CUDA版本的PyTorch"
        pip install torch>=2.0.0 torchaudio>=2.0.0 --index-url https://download.pytorch.org/whl/cu121
    else
        log_warning "未检测到NVIDIA GPU，安装CPU版本的PyTorch"
        pip install torch>=2.0.0 torchaudio>=2.0.0 --index-url https://download.pytorch.org/whl/cpu
    fi
    
    if [ $? -eq 0 ]; then
        log_success "PyTorch安装完成"
    else
        log_error "PyTorch安装失败"
        exit 1
    fi
}

# 安装其他依赖
install_other_deps() {
    log_info "安装其他依赖..."
    
    pip install pydantic>=2.0.0 loguru>=0.7.0 PyYAML>=6.0 psutil>=5.9.0
    
    # 可选依赖
    log_info "安装可选依赖..."
    pip install gpustat>=1.1.0 || log_warning "gpustat安装失败（可选依赖）"
    
    log_success "其他依赖安装完成"
}

# 安装DeepFilterNet（可选）
install_deepfilternet() {
    log_info "尝试安装DeepFilterNet降噪模型..."
    
    # DeepFilterNet需要特殊安装
    pip install deepfilternet || {
        log_warning "DeepFilterNet安装失败，将使用简化降噪器"
        log_info "如需完整降噪功能，请手动安装："
        log_info "  pip install deepfilternet"
        return 1
    }
    
    log_success "DeepFilterNet安装完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    python3 -c "
import librosa
import soundfile
import torch
import torchaudio
import numpy as np
import yaml
from loguru import logger
print('✅ 所有基础依赖验证通过')
"
    
    if [ $? -eq 0 ]; then
        log_success "基础依赖验证通过"
    else
        log_error "依赖验证失败"
        exit 1
    fi
    
    # 验证DeepFilterNet（可选）
    python3 -c "
try:
    import df
    print('✅ DeepFilterNet可用')
except ImportError:
    print('⚠️  DeepFilterNet不可用，将使用简化降噪器')
" 2>/dev/null
}

# 主函数
main() {
    echo "🚀 开始安装音频预处理依赖..."
    echo ""
    
    # 检查是否在项目根目录
    if [ ! -f "README.md" ] || [ ! -d "audio_processor" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    check_python
    echo ""
    
    install_basic_deps
    echo ""
    
    install_pytorch
    echo ""
    
    install_other_deps
    echo ""
    
    install_deepfilternet
    echo ""
    
    verify_installation
    echo ""
    
    log_success "🎉 音频预处理依赖安装完成！"
    echo ""
    log_info "下一步:"
    echo "1. 运行测试: python scripts/test_preprocessing.py"
    echo "2. 启用预处理: 在 configs/preprocessing.yaml 中设置 enable: true"
    echo "3. 使用预处理: python -m audio_processor whisper audio.wav --enable-preprocessing"
}

# 运行主函数
main "$@"
