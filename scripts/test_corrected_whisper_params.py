#!/usr/bin/env python3
"""
测试修正后的 Whisper 转录参数
验证词级时间戳和其他参数的正确使用
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from audio_processor.transcribing.transcriber import WhisperTranscriber
from loguru import logger


def test_corrected_timestamp_params():
    """测试修正后的时间戳参数"""
    print("=" * 60)
    print("测试修正后的时间戳参数")
    print("=" * 60)
    
    try:
        transcriber = WhisperTranscriber("whisper")
        
        # 测试不同的时间戳参数
        test_cases = [
            {
                "name": "默认时间戳（句子级）",
                "params": {}
            },
            {
                "name": "明确指定句子级时间戳",
                "params": {"timestamp_granularity": "segment"}
            },
            {
                "name": "词级时间戳",
                "params": {"timestamp_granularity": "word"}
            },
            {
                "name": "禁用时间戳",
                "params": {"return_timestamps": False}
            },
            {
                "name": "启用时间戳（布尔值）",
                "params": {"return_timestamps": True}
            }
        ]
        
        for test_case in test_cases:
            print(f"\n测试: {test_case['name']}")
            print(f"参数: {test_case['params']}")
            
            try:
                generate_kwargs, pipeline_kwargs = transcriber._build_transcription_params(**test_case['params'])
                
                print(f"  generate_kwargs: {generate_kwargs}")
                print(f"  pipeline_kwargs: {pipeline_kwargs}")
                
                # 验证 return_timestamps 参数的正确性
                if "return_timestamps" in pipeline_kwargs:
                    rt_value = pipeline_kwargs["return_timestamps"]
                    if test_case['params'].get('timestamp_granularity') == 'word':
                        expected = "word"
                    elif test_case['params'].get('return_timestamps') is False:
                        expected = False
                    else:
                        expected = True
                    
                    if rt_value == expected:
                        print(f"  ✓ return_timestamps 值正确: {rt_value}")
                    else:
                        print(f"  ✗ return_timestamps 值错误: 期望 {expected}, 实际 {rt_value}")
                else:
                    print(f"  ✗ 缺少 return_timestamps 参数")
                
                print("  ✓ 参数构建成功")
                
            except Exception as e:
                print(f"  ✗ 参数构建失败: {e}")
        
        print("\n✓ 时间戳参数测试完成")
        
    except Exception as e:
        print(f"✗ 时间戳参数测试失败: {e}")


def test_official_usage_patterns():
    """测试官方用法模式"""
    print("\n" + "=" * 60)
    print("测试官方用法模式")
    print("=" * 60)
    
    try:
        transcriber = WhisperTranscriber("whisper")
        
        print("根据官方文档，正确的用法应该是:")
        print("1. 句子级时间戳: pipe(audio, return_timestamps=True)")
        print("2. 词级时间戳: pipe(audio, return_timestamps='word')")
        
        # 测试我们的实现是否符合官方用法
        test_cases = [
            {
                "description": "句子级时间戳",
                "params": {"return_timestamps": True},
                "expected_pipeline_kwargs": {"return_timestamps": True}
            },
            {
                "description": "词级时间戳",
                "params": {"timestamp_granularity": "word"},
                "expected_pipeline_kwargs": {"return_timestamps": "word"}
            },
            {
                "description": "禁用时间戳",
                "params": {"return_timestamps": False},
                "expected_pipeline_kwargs": {"return_timestamps": False}
            }
        ]
        
        for test_case in test_cases:
            print(f"\n测试: {test_case['description']}")
            
            generate_kwargs, pipeline_kwargs = transcriber._build_transcription_params(**test_case['params'])
            
            expected = test_case['expected_pipeline_kwargs']
            actual_rt = pipeline_kwargs.get('return_timestamps')
            expected_rt = expected.get('return_timestamps')
            
            if actual_rt == expected_rt:
                print(f"  ✓ 符合官方用法: return_timestamps={actual_rt}")
            else:
                print(f"  ✗ 不符合官方用法: 期望 {expected_rt}, 实际 {actual_rt}")
        
        print("\n✓ 官方用法模式测试完成")
        
    except Exception as e:
        print(f"✗ 官方用法模式测试失败: {e}")


def test_generate_kwargs_separation():
    """测试 generate_kwargs 和 pipeline_kwargs 的正确分离"""
    print("\n" + "=" * 60)
    print("测试参数分离")
    print("=" * 60)
    
    try:
        transcriber = WhisperTranscriber("whisper")
        
        # 测试完整参数集
        params = {
            "language": "zh",
            "task": "transcribe",
            "temperature": 0.2,
            "max_new_tokens": 512,
            "num_beams": 3,
            "timestamp_granularity": "word",
            "return_timestamps": True,
            "no_speech_threshold": 0.8
        }
        
        generate_kwargs, pipeline_kwargs = transcriber._build_transcription_params(**params)
        
        print("完整参数测试:")
        print(f"  输入参数: {params}")
        print(f"  generate_kwargs: {generate_kwargs}")
        print(f"  pipeline_kwargs: {pipeline_kwargs}")
        
        # 验证参数分离的正确性
        print("\n验证参数分离:")
        
        # generate_kwargs 应该包含的参数
        expected_generate_params = [
            "language", "task", "temperature", "max_new_tokens", 
            "num_beams", "no_speech_threshold", "condition_on_prev_tokens",
            "compression_ratio_threshold", "logprob_threshold"
        ]
        
        for param in expected_generate_params:
            if param in generate_kwargs:
                print(f"  ✓ {param} 在 generate_kwargs 中")
            else:
                print(f"  ✗ {param} 不在 generate_kwargs 中")
        
        # pipeline_kwargs 应该只包含 return_timestamps
        if "return_timestamps" in pipeline_kwargs:
            print(f"  ✓ return_timestamps 在 pipeline_kwargs 中: {pipeline_kwargs['return_timestamps']}")
        else:
            print(f"  ✗ return_timestamps 不在 pipeline_kwargs 中")
        
        # 确保 return_timestamps 不在 generate_kwargs 中
        if "return_timestamps" not in generate_kwargs:
            print(f"  ✓ return_timestamps 正确地不在 generate_kwargs 中")
        else:
            print(f"  ✗ return_timestamps 错误地在 generate_kwargs 中")
        
        print("\n✓ 参数分离测试完成")
        
    except Exception as e:
        print(f"✗ 参数分离测试失败: {e}")


def main():
    """主测试函数"""
    print("测试修正后的 Whisper 转录参数")
    print("=" * 60)
    
    # 测试修正后的时间戳参数
    test_corrected_timestamp_params()
    
    # 测试官方用法模式
    test_official_usage_patterns()
    
    # 测试参数分离
    test_generate_kwargs_separation()
    
    print("\n" + "=" * 60)
    print("所有测试完成")
    print("=" * 60)
    
    print("\n总结:")
    print("1. ✓ 修正了时间戳参数的使用方法")
    print("2. ✓ 符合官方文档的用法: return_timestamps='word'")
    print("3. ✓ 正确分离了 generate_kwargs 和 pipeline_kwargs")
    print("4. ✓ 支持通过配置文件和参数控制时间戳粒度")


if __name__ == "__main__":
    main()
