#!/bin/bash

# Rust和DeepFilterNet安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 检查系统要求
check_system() {
    log_step "检查系统环境..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        log_info "检测到Linux系统"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        log_info "检测到macOS系统"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    # 检查必要工具
    for tool in curl gcc; do
        if ! command -v $tool &> /dev/null; then
            log_error "$tool 未安装，请先安装"
            exit 1
        fi
    done
    
    log_success "系统环境检查通过"
}

# 安装Rust
install_rust() {
    log_step "安装Rust编程语言..."
    
    # 检查Rust是否已安装
    if command -v rustc &> /dev/null; then
        local rust_version=$(rustc --version)
        log_info "Rust已安装: $rust_version"
        
        # 询问是否更新
        read -p "是否要更新Rust到最新版本? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "更新Rust..."
            rustup update
        fi
    else
        log_info "开始安装Rust..."
        
        # 下载并安装rustup
        curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
        
        # 添加到PATH
        source ~/.cargo/env
        
        log_success "Rust安装完成"
    fi
    
    # 验证安装
    if command -v rustc &> /dev/null; then
        local rust_version=$(rustc --version)
        local cargo_version=$(cargo --version)
        log_success "Rust验证成功:"
        log_info "  - $rust_version"
        log_info "  - $cargo_version"
    else
        log_error "Rust安装失败"
        exit 1
    fi
}

# 配置Rust环境
configure_rust() {
    log_step "配置Rust环境..."
    
    # 确保cargo在PATH中
    if ! command -v cargo &> /dev/null; then
        log_info "添加Cargo到PATH..."
        export PATH="$HOME/.cargo/bin:$PATH"
        
        # 添加到shell配置文件
        for shell_config in ~/.bashrc ~/.zshrc ~/.profile; do
            if [[ -f "$shell_config" ]]; then
                if ! grep -q "cargo/bin" "$shell_config"; then
                    echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> "$shell_config"
                    log_info "已添加Cargo路径到 $shell_config"
                fi
            fi
        done
    fi
    
    # 安装必要的Rust组件
    log_info "安装Rust组件..."
    rustup component add rustfmt clippy
    
    log_success "Rust环境配置完成"
}

# 安装系统依赖
install_system_deps() {
    log_step "安装系统依赖..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux系统依赖
        if command -v apt-get &> /dev/null; then
            # Ubuntu/Debian
            log_info "安装Ubuntu/Debian依赖..."
            sudo apt-get update
            sudo apt-get install -y build-essential pkg-config libssl-dev
        elif command -v yum &> /dev/null; then
            # CentOS/RHEL
            log_info "安装CentOS/RHEL依赖..."
            sudo yum groupinstall -y "Development Tools"
            sudo yum install -y openssl-devel
        elif command -v pacman &> /dev/null; then
            # Arch Linux
            log_info "安装Arch Linux依赖..."
            sudo pacman -S --noconfirm base-devel openssl
        else
            log_warning "未识别的Linux发行版，请手动安装build-essential和openssl-dev"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS系统依赖
        log_info "检查macOS依赖..."
        if ! command -v brew &> /dev/null; then
            log_warning "建议安装Homebrew来管理依赖"
        else
            brew install openssl
        fi
    fi
    
    log_success "系统依赖安装完成"
}

# 安装DeepFilterNet
install_deepfilternet() {
    log_step "安装DeepFilterNet..."
    
    # 确保在正确的conda环境中
    if [[ -n "$CONDA_DEFAULT_ENV" ]]; then
        log_info "当前conda环境: $CONDA_DEFAULT_ENV"
    else
        log_warning "未检测到conda环境，请确保在正确的Python环境中"
    fi
    
    # 设置环境变量（可能需要）
    export CARGO_NET_GIT_FETCH_WITH_CLI=true
    
    log_info "开始安装DeepFilterNet（这可能需要几分钟）..."
    
    # 尝试安装DeepFilterNet
    if pip install deepfilternet; then
        log_success "DeepFilterNet安装成功"
    else
        log_error "DeepFilterNet安装失败"
        log_info "尝试替代安装方法..."
        
        # 尝试从源码安装
        log_info "尝试从源码安装..."
        pip install --no-binary=deepfilternet deepfilternet || {
            log_error "源码安装也失败了"
            return 1
        }
    fi
}

# 验证安装
verify_installation() {
    log_step "验证安装..."
    
    # 验证Rust
    log_info "验证Rust安装..."
    rustc --version
    cargo --version
    
    # 验证DeepFilterNet
    log_info "验证DeepFilterNet安装..."
    python -c "
try:
    import df
    print('✅ DeepFilterNet导入成功')
    
    # 尝试初始化
    from df.enhance import init_df
    print('✅ DeepFilterNet模块可用')
    
except ImportError as e:
    print(f'❌ DeepFilterNet导入失败: {e}')
    exit(1)
except Exception as e:
    print(f'⚠️  DeepFilterNet导入成功，但初始化可能需要模型下载: {e}')
"
    
    if [ $? -eq 0 ]; then
        log_success "DeepFilterNet验证成功"
    else
        log_error "DeepFilterNet验证失败"
        return 1
    fi
}

# 测试预处理功能
test_preprocessing() {
    log_step "测试音频预处理功能..."
    
    # 运行预处理测试
    if python scripts/test_preprocessing.py; then
        log_success "预处理功能测试通过"
    else
        log_warning "预处理功能测试有问题，但DeepFilterNet已安装"
    fi
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    # 这里可以添加清理逻辑
}

# 主函数
main() {
    echo "🦀 Rust和DeepFilterNet安装脚本"
    echo "=================================="
    echo ""
    
    # 检查是否在项目根目录
    if [ ! -f "README.md" ] || [ ! -d "audio_processor" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 设置错误处理
    trap cleanup EXIT
    
    # 执行安装步骤
    check_system
    echo ""
    
    install_rust
    echo ""
    
    configure_rust
    echo ""
    
    install_system_deps
    echo ""
    
    install_deepfilternet
    echo ""
    
    verify_installation
    echo ""
    
    test_preprocessing
    echo ""
    
    log_success "🎉 安装完成！"
    echo ""
    log_info "下一步:"
    echo "1. 重新启动终端或运行: source ~/.cargo/env"
    echo "2. 测试预处理功能: python scripts/test_preprocessing.py"
    echo "3. 使用预处理转录: python -m audio_processor whisper audio.wav --enable-preprocessing"
    echo ""
    log_info "如果遇到问题，请检查:"
    echo "- Rust是否正确安装: rustc --version"
    echo "- DeepFilterNet是否可用: python -c 'import df; print(\"OK\")'"
}

# 运行主函数
main "$@"
