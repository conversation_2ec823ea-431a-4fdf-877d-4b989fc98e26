#!/usr/bin/env python3
"""
Whisper适配器示例
提供与原始OpenAI Whisper库兼容的API，但在内部使用项目的Whisper实现
"""

import os
import sys
import time
import argparse
from pathlib import Path
from typing import Dict, Any, Optional, List, Union

# 添加项目根目录到Python路径
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.insert(0, str(project_root))

# 导入项目的WhisperTranscriber
from audio_processor.transcribing.transcriber import WhisperTranscriber


class WhisperModelAdapter:
    """OpenAI Whisper模型适配器
    提供与原始whisper.model接口兼容的API
    """
    
    def __init__(self, model_size: str = "medium"):
        """初始化适配器
        
        Args:
            model_size: 模型大小 (tiny, base, small, medium, large)
                        注意：这个参数仅用于兼容原始API，实际使用的模型由配置文件决定
        """
        self.model_size = model_size
        print(f"注意：指定的模型大小'{model_size}'仅用于兼容原始API")
        print("实际使用的模型由配置文件决定，详见configs/whisper.yaml")
        
        # 初始化项目的转录器
        self.transcriber = WhisperTranscriber(enable_preprocessing=False)
        
        # 获取配置的模型名称
        self.model_name = self.transcriber.get_config_value("model.name", "openai/whisper-large-v3-turbo")
        print(f"使用配置的模型: {self.model_name}")
    
    def transcribe(
        self, 
        audio_path: str,
        language: Optional[str] = None,
        task: str = "transcribe",
        verbose: bool = True,
        word_timestamps: bool = False,
        condition_on_previous_text: bool = True,
        temperature: Union[float, List[float]] = 0.0,
        beam_size: int = 5,
        best_of: int = 5,
        fp16: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """转录音频文件
        
        Args:
            audio_path: 音频文件路径
            language: 语言代码（如'zh', 'en'等）
            task: 任务类型（'transcribe'或'translate'）
            verbose: 是否显示详细信息
            word_timestamps: 是否生成词级时间戳
            condition_on_previous_text: 是否基于前面的文本进行条件生成
            temperature: 采样温度，可以是浮点数或浮点数列表
            beam_size: beam search宽度
            best_of: 保留的候选数量
            fp16: 是否使用半精度浮点数
            
        Returns:
            转录结果字典，格式与原始Whisper库兼容
        """
        if verbose:
            print(f"开始转录: {audio_path}")
            print(f"参数: language={language}, task={task}, word_timestamps={word_timestamps}")
        
        start_time = time.time()
        
        # 转换参数到项目的WhisperTranscriber格式
        timestamp_granularity = "word" if word_timestamps else "segment"
        
        # 执行转录
        result = self.transcriber.transcribe(
            audio_path,
            language=language,
            task=task,
            timestamp_granularity=timestamp_granularity,
            return_timestamps=True,
            temperature=temperature,
            num_beams=beam_size,  # beam_size 映射到 num_beams
            condition_on_prev_tokens=condition_on_previous_text,
            # 其他参数可以按需映射
            **kwargs
        )
        
        # 转换结果到原始Whisper格式
        original_format_result = self._convert_to_original_format(result, word_timestamps)
        
        if verbose:
            transcribe_time = time.time() - start_time
            print(f"转录完成，耗时: {transcribe_time:.2f}秒")
            print(f"文本: {original_format_result['text'][:100]}...")
        
        return original_format_result
    
    def _convert_to_original_format(self, result: Dict[str, Any], word_timestamps: bool) -> Dict[str, Any]:
        """将项目的转录结果转换为原始Whisper格式
        
        Args:
            result: 项目的转录结果
            word_timestamps: 是否包含词级时间戳
            
        Returns:
            原始Whisper格式的结果
        """
        # 基本结构
        original_format = {
            "text": result["text"],
            "language": "zh",  # 默认中文，实际应从结果中提取
            "segments": []
        }
        
        # 添加分段信息
        if "chunks" in result:
            for i, chunk in enumerate(result["chunks"]):
                text = chunk["text"]
                start, end = chunk["timestamp"] if "timestamp" in chunk else (0, 0)
                
                segment = {
                    "id": i,
                    "seek": 0,
                    "start": start,
                    "end": end,
                    "text": text,
                    "tokens": [],  # 原始格式需要这个字段，但我们不填充它
                    "temperature": 0.0,
                    "avg_logprob": 0.0,
                    "compression_ratio": 1.0,
                    "no_speech_prob": 0.0
                }
                
                # 如果有词级时间戳
                if word_timestamps and len(chunk["words"]) > 0:
                    segment["words"] = []
                    for word_info in chunk["words"]:
                        word_start, word_end = word_info["timestamp"] if "timestamp" in word_info else (0, 0)
                        segment["words"].append({
                            "word": word_info["text"],
                            "start": word_start,
                            "end": word_end,
                            "probability": 1.0  # 默认值
                        })
                
                original_format["segments"].append(segment)
        
        return original_format


def load_model(name: str) -> WhisperModelAdapter:
    """加载模型的兼容函数
    
    Args:
        name: 模型名称 (tiny, base, small, medium, large)
        
    Returns:
        WhisperModelAdapter实例
    """
    return WhisperModelAdapter(model_size=name)


def transcribe_with_adapter(audio_path, model_size="medium", language=None, word_timestamps=True):
    """使用适配器进行转录
    
    Args:
        audio_path: 音频文件路径
        model_size: 模型大小
        language: 语言代码
        word_timestamps: 是否启用词级时间戳
        
    Returns:
        转录结果
    """
    print(f"使用适配器加载Whisper模型: {model_size}")
    start_time = time.time()
    
    # 使用适配器加载模型
    model = load_model(model_size)
    
    print(f"模型加载完成，耗时: {time.time() - start_time:.2f}秒")
    print(f"开始转录文件: {audio_path}")
    
    transcribe_start = time.time()
    
    # 进行转录
    result = model.transcribe(
        audio_path,
        language=language,
        task="transcribe",
        verbose=True,
        word_timestamps=word_timestamps,
        condition_on_previous_text=True,
        temperature=0.0,
        beam_size=5,
        best_of=5,
        fp16=False
    )
    
    transcribe_time = time.time() - transcribe_start
    print(f"转录完成，耗时: {transcribe_time:.2f}秒")
    
    # 输出转录结果
    print("\n转录文本:")
    print(result["text"])
    
    # 输出分段信息
    print("\n分段信息:")
    for i, segment in enumerate(result["segments"]):
        print(f"[{segment['start']:.2f} --> {segment['end']:.2f}] {segment['text']}")
    
    # 如果启用了词级时间戳，展示词级信息
    if word_timestamps and "words" in result["segments"][0]:
        print("\n词级时间戳示例（前10个词）:")
        words_shown = 0
        for segment in result["segments"]:
            if "words" in segment:
                for word in segment["words"]:
                    print(f"'{word['word']}' [{word['start']:.2f}s - {word['end']:.2f}s]")
                    words_shown += 1
                    if words_shown >= 10:
                        break
            if words_shown >= 10:
                break
    
    return result


def save_result(result, output_path):
    """保存转录结果到JSON文件
    
    Args:
        result: 转录结果
        output_path: 输出文件路径
    """
    import json
    
    print(f"保存结果到: {output_path}")
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)


def main():
    parser = argparse.ArgumentParser(description="使用Whisper适配器进行音频转录")
    parser.add_argument("audio_path", help="音频文件路径")
    parser.add_argument("--model", default="medium", 
                        choices=["tiny", "base", "small", "medium", "large"],
                        help="Whisper模型大小")
    parser.add_argument("--language", default=None, help="指定语言代码（如zh, en, ar等），不指定则自动检测")
    parser.add_argument("--no-word-timestamps", action="store_true", help="禁用词级时间戳")
    parser.add_argument("--output", help="输出JSON文件路径")
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not Path(args.audio_path).exists():
        print(f"错误：文件不存在 {args.audio_path}")
        return
    
    # 运行转录
    result = transcribe_with_adapter(
        args.audio_path, 
        model_size=args.model, 
        language=args.language,
        word_timestamps=not args.no_word_timestamps
    )
    
    # 保存结果
    if args.output:
        save_result(result, args.output)
    else:
        # 自动生成输出文件名
        output_path = f"output/whisper_adapter_{Path(args.audio_path).stem}.json"
        Path("output").mkdir(exist_ok=True)
        save_result(result, output_path)


if __name__ == "__main__":
    main() 