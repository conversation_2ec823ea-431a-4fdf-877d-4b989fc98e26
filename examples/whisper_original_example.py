#!/usr/bin/env python3
"""
使用原始OpenAI Whisper库的转录示例
这个示例展示了如何使用原生的whisper库进行音频转录
"""

import whisper
import argparse
import time
from pathlib import Path


def transcribe_with_whisper(audio_path, model_size="medium", language=None, word_timestamps=True):
    """使用原始OpenAI Whisper库进行转录
    
    Args:
        audio_path: 音频文件路径
        model_size: 模型大小 (tiny, base, small, medium, large)
        language: 指定语言代码 (如 'zh', 'en', 'ar' 等)，None表示自动检测
        word_timestamps: 是否启用词级时间戳
        
    Returns:
        转录结果字典
    """
    print(f"开始加载Whisper模型: {model_size}")
    start_time = time.time()
    
    # 加载 Whisper 模型
    model = whisper.load_model(model_size)
    
    print(f"模型加载完成，耗时: {time.time() - start_time:.2f}秒")
    print(f"开始转录文件: {audio_path}")
    
    transcribe_start = time.time()
    
    # 进行转录，使用 transcribe() 方法
    result = model.transcribe(
        audio_path,
        language=language,              # 指定语言（None表示自动检测）
        task="transcribe",              # 可选 "transcribe" 或 "translate"
        verbose=True,                   # 显示详细转录信息（如进度）
        word_timestamps=word_timestamps,# 输出词级时间戳
        condition_on_previous_text=True,# 使用上下文连续性进行转录（对长音频效果好）
        temperature=0.0,                # 解码随机性（越低越确定），可为 float 或 float 列表
        beam_size=5,                    # Beam search 的宽度（提高准确率但会变慢）
        best_of=5,                      # sampling 时保留的候选数量（仅 temperature>0 时使用）
        fp16=False                      # 是否启用 float16 推理（False 兼容性好，但速度稍慢）
    )
    
    transcribe_time = time.time() - transcribe_start
    print(f"转录完成，耗时: {transcribe_time:.2f}秒")
    
    # 输出转录结果
    print("\n转录文本:")
    print(result["text"])
    
    # 输出分段信息
    print("\n分段信息:")
    for i, segment in enumerate(result["segments"]):
        print(f"[{segment['start']:.2f} --> {segment['end']:.2f}] {segment['text']}")
    
    # 如果启用了词级时间戳，展示词级信息
    if word_timestamps and "words" in result["segments"][0]:
        print("\n词级时间戳示例（前10个词）:")
        words_shown = 0
        for segment in result["segments"]:
            if "words" in segment:
                for word in segment["words"]:
                    print(f"'{word['word']}' [{word['start']:.2f}s - {word['end']:.2f}s]")
                    words_shown += 1
                    if words_shown >= 10:
                        break
            if words_shown >= 10:
                break
    
    return result


def save_result(result, output_path):
    """保存转录结果到JSON文件
    
    Args:
        result: 转录结果
        output_path: 输出文件路径
    """
    import json
    
    print(f"保存结果到: {output_path}")
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)


def main():
    parser = argparse.ArgumentParser(description="使用原始OpenAI Whisper库进行音频转录")
    parser.add_argument("audio_path", help="音频文件路径")
    parser.add_argument("--model", default="medium", 
                        choices=["tiny", "base", "small", "medium", "large"],
                        help="Whisper模型大小")
    parser.add_argument("--language", default=None, help="指定语言代码（如zh, en, ar等），不指定则自动检测")
    parser.add_argument("--no-word-timestamps", action="store_true", help="禁用词级时间戳")
    parser.add_argument("--output", help="输出JSON文件路径")
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not Path(args.audio_path).exists():
        print(f"错误：文件不存在 {args.audio_path}")
        return
    
    # 运行转录
    result = transcribe_with_whisper(
        args.audio_path, 
        model_size=args.model, 
        language=args.language,
        word_timestamps=not args.no_word_timestamps
    )
    
    # 保存结果
    if args.output:
        save_result(result, args.output)
    else:
        # 自动生成输出文件名
        output_path = f"output/whisper_original_{Path(args.audio_path).stem}.json"
        Path("output").mkdir(exist_ok=True)
        save_result(result, output_path)


if __name__ == "__main__":
    main() 