#!/usr/bin/env python3
"""
CAMeL Tools使用示例
演示如何在主项目中使用camel-tools功能
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from audio_processor.api.camel_service import CamelToolsService
from loguru import logger

def main():
    """主函数"""
    # 初始化CAMeL Tools服务
    camel_service = CamelToolsService()
    
    # 测试文本
    arabic_text = "مرحبا بكم في عالم معالجة اللغة العربية"
    
    print("CAMeL Tools功能演示")
    print("=" * 50)
    
    # 1. 检查环境
    print("1. 检查camel-tools环境...")
    env_check = camel_service.check_environment()
    if env_check["success"]:
        print(f"   ✓ 环境正常")
        print(f"   - CAMeL Tools版本: {env_check['data']['camel_tools_version']}")
        print(f"   - Transformers版本: {env_check['data']['transformers_version']}")
    else:
        print(f"   ✗ 环境检查失败: {env_check['error']}")
        return
    
    print()
    
    # 2. 分词测试
    print("2. 阿拉伯语分词...")
    print(f"   输入: {arabic_text}")
    
    tokenize_result = camel_service.tokenize_arabic(arabic_text)
    if tokenize_result["success"]:
        tokens = tokenize_result["data"]["tokens"]
        print(f"   输出: {tokens}")
        print(f"   词数: {len(tokens)}")
    else:
        print(f"   ✗ 分词失败: {tokenize_result['error']}")
    
    print()
    
    # 3. 形态学分析
    print("3. 形态学分析...")
    morph_result = camel_service.morphological_analysis(arabic_text)
    if morph_result["success"]:
        analyses = morph_result["data"]["morphological_analysis"]
        for word_analysis in analyses[:2]:  # 只显示前两个词的分析
            word = word_analysis["word"]
            print(f"   词: {word}")
            for i, analysis in enumerate(word_analysis["analyses"][:2]):
                print(f"     分析{i+1}: 词根={analysis['lemma']}, 词性={analysis['pos']}")
    else:
        print(f"   ✗ 形态学分析失败: {morph_result['error']}")
    
    print()
    
    # 4. 转写测试
    print("4. 阿拉伯语转写...")
    transliterate_result = camel_service.transliterate(arabic_text, "ar2bw")
    if transliterate_result["success"]:
        original = transliterate_result["data"]["original"]
        transliterated = transliterate_result["data"]["transliterated"]
        print(f"   原文: {original}")
        print(f"   转写: {transliterated}")
    else:
        print(f"   ✗ 转写失败: {transliterate_result['error']}")
    
    print()
    print("演示完成！")

def test_integration_with_qwen():
    """测试与Qwen模型的集成"""
    print("\n测试与Qwen模型的集成...")
    print("=" * 30)
    
    try:
        # 这里可以添加与Qwen模型的集成测试
        # 例如：使用camel-tools预处理阿拉伯语文本，然后送入Qwen进行翻译
        
        camel_service = CamelToolsService()
        
        # 示例：阿拉伯语文本预处理
        arabic_text = "كيف حالك اليوم؟"
        
        # 1. 使用camel-tools进行分词
        tokenize_result = camel_service.tokenize_arabic(arabic_text)
        
        if tokenize_result["success"]:
            tokens = tokenize_result["data"]["tokens"]
            print(f"分词结果: {tokens}")
            
            # 2. 这里可以将分词结果传递给Qwen模型进行进一步处理
            # 例如：翻译、文本生成等
            processed_text = " ".join(tokens)
            print(f"预处理后的文本: {processed_text}")
            
            # 注意：实际的Qwen模型调用需要在主环境中进行
            print("提示：将预处理后的文本传递给Qwen模型进行翻译...")
            
        else:
            print(f"预处理失败: {tokenize_result['error']}")
            
    except Exception as e:
        logger.error(f"集成测试失败: {e}")

if __name__ == "__main__":
    main()
    test_integration_with_qwen()
