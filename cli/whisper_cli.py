"""
Whisper 音频转录命令行工具
"""

import sys
import json
import argparse
from pathlib import Path

from loguru import logger

from ..transcribing.transcriber import WhisperTranscriber
from ..utils.io import save_transcription_result


def whisper_cli(args=None):
    """Whisper 转录命令行入口"""
    # 如果没有传递参数，则自己解析
    if args is None:
        parser = argparse.ArgumentParser(
            description="音频转录工具 - 基于 OpenAI Whisper Large V3 Turbo",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
示例用法:
  python -m audio_processor whisper audio.wav                    # 转录单个文件
  python -m audio_processor whisper audio.wav -o result.json     # 指定输出文件
  python -m audio_processor whisper audio.wav -l zh              # 指定语言为中文
  python -m audio_processor whisper *.wav                        # 批量转录多个文件
  python -m audio_processor whisper audio.wav --enable-preprocessing   # 启用预处理
  python -m audio_processor whisper audio.wav --disable-preprocessing  # 禁用预处理
            """
        )

        parser.add_argument(
            "audio_files",
            nargs="+",
            help="音频文件路径（支持多个文件）"
        )

        parser.add_argument(
            "-o", "--output",
            help="输出文件路径（单个文件时）或输出目录（多个文件时）"
        )

        parser.add_argument(
            "-c", "--config",
            default="whisper",
            help="配置名称 (默认: whisper)"
        )

        parser.add_argument(
            "-l", "--language",
            help="指定音频语言 (如: zh, en, ja 等，留空自动检测)"
        )

        parser.add_argument(
            "-f", "--format",
            choices=["json", "txt"],
            help="输出格式"
        )

        parser.add_argument(
            "--timestamps",
            action="store_true",
            help="包含时间戳信息"
        )

        parser.add_argument(
            "--batch",
            action="store_true",
            help="批量处理模式（即使只有一个文件）"
        )

        parser.add_argument(
            "--clear-cache",
            action="store_true",
            help="处理前清理GPU缓存"
        )

        parser.add_argument(
            "--enable-preprocessing",
            action="store_true",
            help="启用音频预处理（降噪、VAD检测、语音段分割）"
        )

        parser.add_argument(
            "--disable-preprocessing",
            action="store_true",
            help="禁用音频预处理"
        )

        # 转录参数
        parser.add_argument(
            "--task",
            choices=["transcribe", "translate"],
            help="转录任务类型: transcribe(转录) 或 translate(翻译为英文)"
        )

        parser.add_argument(
            "--temperature",
            type=float,
            help="温度参数 (0.0-1.0, 0.0=确定性, 1.0=随机性)"
        )

        parser.add_argument(
            "--max-tokens",
            type=int,
            help="最大生成token数 (默认: 448)"
        )

        parser.add_argument(
            "--num-beams",
            type=int,
            help="beam search数量 (1=贪婪搜索, >1=beam search)"
        )

        parser.add_argument(
            "--no-speech-threshold",
            type=float,
            help="无语音阈值 (0.0-1.0, 默认: 0.6)"
        )

        parser.add_argument(
            "--compression-ratio-threshold",
            type=float,
            help="压缩比阈值 (用于检测重复, 默认: 1.35)"
        )

        parser.add_argument(
            "--word-timestamps",
            action="store_true",
            help="生成词级时间戳（而非句子级）"
        )

        parser.add_argument(
            "--timestamp-granularity",
            choices=["segment", "word"],
            help="时间戳粒度: segment(句子级) 或 word(词级)"
        )

        parser.add_argument(
            "--initial-prompt",
            type=str,
            help="初始提示词，用于引导模型生成特定风格的文本"
        )

        args = parser.parse_args()

    try:
        # 确定预处理设置
        enable_preprocessing = None
        if args.enable_preprocessing and args.disable_preprocessing:
            print("错误：不能同时启用和禁用预处理")
            return
        elif args.enable_preprocessing:
            enable_preprocessing = True
            print("预处理功能已启用")
        elif args.disable_preprocessing:
            enable_preprocessing = False
            print("预处理功能已禁用")

        # 创建转录器
        print("正在初始化音频转录器...")
        transcriber = WhisperTranscriber(args.config, enable_preprocessing=enable_preprocessing)

        # 清理GPU缓存（如果需要）
        if args.clear_cache:
            transcriber.monitor.clear_gpu_cache()

        # 处理输出格式设置
        if args.format:
            transcriber.update_config({"output": {"format": args.format}})

        if args.timestamps:
            transcriber.update_config({"output": {"include_timestamps": True}})

        # 检查文件数量
        audio_files = args.audio_files

        if len(audio_files) == 1 and not args.batch:
            # 单文件处理
            audio_file = audio_files[0]
            print(f"\n开始转录: {audio_file}")

            # 构建转录参数
            transcribe_kwargs = {}
            if args.language:
                transcribe_kwargs["language"] = args.language
            if args.task:
                transcribe_kwargs["task"] = args.task
            if args.temperature is not None:
                transcribe_kwargs["temperature"] = args.temperature
            if args.max_tokens:
                transcribe_kwargs["max_new_tokens"] = args.max_tokens
            if args.num_beams:
                transcribe_kwargs["num_beams"] = args.num_beams
            if args.no_speech_threshold is not None:
                transcribe_kwargs["no_speech_threshold"] = args.no_speech_threshold
            if args.compression_ratio_threshold is not None:
                transcribe_kwargs["compression_ratio_threshold"] = args.compression_ratio_threshold
            if args.word_timestamps:
                transcribe_kwargs["timestamp_granularity"] = "word"
            elif args.timestamp_granularity:
                transcribe_kwargs["timestamp_granularity"] = args.timestamp_granularity
            if args.initial_prompt:
                transcribe_kwargs["initial_prompt"] = args.initial_prompt

            result = transcriber.transcribe(audio_file, **transcribe_kwargs)

            # 确定输出路径
            if args.output:
                output_path = args.output
            else:
                audio_path = Path(audio_file)
                output_format = transcriber.get_config_value("output.format", "json")
                output_ext = "json" if output_format == "json" else "txt"
                output_path = audio_path.parent / f"{audio_path.stem}_transcription.{output_ext}"

            # 保存结果
            save_transcription_result(result, str(output_path), transcriber.get_config_value("output.format", "json"))

            # 显示结果
            print("\n" + "="*60)
            print("转录结果:")
            print("="*60)
            print(result["text"])
            print("="*60)
            print(f"处理时间: {result['processing_time']:.2f}秒")
            print(f"结果已保存到: {output_path}")

        else:
            # 批量处理
            print(f"\n开始批量转录 {len(audio_files)} 个文件...")

            # 构建转录参数（与单文件处理相同）
            transcribe_kwargs = {}
            if args.language:
                transcribe_kwargs["language"] = args.language
            if args.task:
                transcribe_kwargs["task"] = args.task
            if args.temperature is not None:
                transcribe_kwargs["temperature"] = args.temperature
            if args.max_tokens:
                transcribe_kwargs["max_new_tokens"] = args.max_tokens
            if args.num_beams:
                transcribe_kwargs["num_beams"] = args.num_beams
            if args.no_speech_threshold is not None:
                transcribe_kwargs["no_speech_threshold"] = args.no_speech_threshold
            if args.compression_ratio_threshold is not None:
                transcribe_kwargs["compression_ratio_threshold"] = args.compression_ratio_threshold
            if args.word_timestamps:
                transcribe_kwargs["timestamp_granularity"] = "word"
            elif args.timestamp_granularity:
                transcribe_kwargs["timestamp_granularity"] = args.timestamp_granularity
            if args.initial_prompt:
                transcribe_kwargs["initial_prompt"] = args.initial_prompt

            results = transcriber.transcribe_batch(audio_files, **transcribe_kwargs)

            # 确定输出目录
            if args.output:
                output_dir = Path(args.output)
            else:
                output_dir = Path("output")

            output_dir.mkdir(exist_ok=True)

            # 保存每个结果
            successful_count = 0
            output_format = transcriber.get_config_value("output.format", "json")

            for i, result in enumerate(results):
                audio_file = audio_files[i]
                audio_name = Path(audio_file).stem

                if "error" not in result:
                    # 成功的转录
                    output_ext = "json" if output_format == "json" else "txt"
                    output_path = output_dir / f"{audio_name}_transcription.{output_ext}"
                    save_transcription_result(result, str(output_path), output_format)
                    successful_count += 1
                    print(f"✓ {audio_file} -> {output_path}")
                else:
                    # 失败的转录
                    print(f"✗ {audio_file}: {result['error']}")

            # 保存批量结果摘要
            summary_path = output_dir / "batch_summary.json"
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)

            print(f"\n批量转录完成:")
            print(f"成功: {successful_count}/{len(audio_files)}")
            print(f"结果保存在: {output_dir}")
            print(f"详细摘要: {summary_path}")

    except KeyboardInterrupt:
        print("\n\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n错误: {e}")
        logger.error(f"转录失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    whisper_cli()
