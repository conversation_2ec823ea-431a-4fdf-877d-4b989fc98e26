flowchart TD
    %% 主程序入口
    A[开始: python -m audio_processor] --> B{检查参数数量}
    
    %% 主命令解析
    B -->|无参数| C[显示主帮助信息]
    B -->|有参数| D{检查第一个参数}
    
    %% 参数检查
    D -->|whisper/qwen/-h/--help| E[解析命令行参数]
    D -->|其他参数| F[插入默认whisper命令]
    F --> E
    
    %% 执行子命令
    E --> G{是否有func属性?}
    G -->|是| H[保存原始argv]
    G -->|否| C
    
    H --> I[设置子命令argv]
    I --> J{判断子命令类型}
    
    %% Whisper子命令流程
    J -->|whisper| K[whisper_cli函数]
    K --> K1[解析whisper参数]
    K1 --> K2[初始化WhisperTranscriber]
    K2 --> K3{是否需要清理GPU缓存?}
    K3 -->|是| K4[清理GPU缓存]
    K3 -->|否| K5{文件数量检查}
    K4 --> K5
    
    %% 单文件处理
    K5 -->|单文件且非批处理| K6[转录单个文件]
    K6 --> K7[确定输出路径]
    K7 --> K8[保存转录结果]
    K8 --> K9[显示结果]
    
    %% 批量处理
    K5 -->|多文件或批处理| K10[批量转录文件]
    K10 --> K11[确定输出目录]
    K11 --> K12[保存每个结果]
    K12 --> K13[保存批量结果摘要]
    K13 --> K14[显示批处理结果]
    
    %% Qwen子命令流程
    J -->|qwen| L[qwen_cli函数]
    L --> L1[解析qwen参数]
    L1 --> L2[设置日志级别]
    L2 --> L3[显示服务器启动信息]
    L3 --> L4[调用run_server启动API服务器]
    
    %% 异常处理
    K9 --> M[恢复原始argv]
    K14 --> M
    L4 --> M
    
    %% 异常处理分支
    K2 -->|异常| K15[错误处理]
    K6 -->|异常| K15
    K10 -->|异常| K15
    L4 -->|异常| L5[显示故障排除信息]
    
    K15 --> M
    L5 --> M
    
    %% 键盘中断处理
    K --> K16[KeyboardInterrupt]
    K16 --> K17[显示用户中断信息]
    K17 --> M
    
    L --> L6[KeyboardInterrupt]
    L6 --> L7[显示服务器已停止信息]
    L7 --> M
    
    %% 结束流程
    M --> N[结束]
