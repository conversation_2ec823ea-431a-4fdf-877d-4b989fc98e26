"""
Audio Processor 统一命令行入口（支持子命令）
"""

# 导入必要的模块
import sys
import argparse

# 导入子命令的函数
from .whisper_cli import whisper_cli


def main():
    """主命令行入口"""
    # 创建 ArgumentParser 对象
    parser = argparse.ArgumentParser(
        description="Audio Processor - 多模态音频处理工具包",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
可用命令:
  whisper       音频转录 (Whisper 模型)

示例用法:
  python -m audio_processor whisper audio.wav           # 音频转录
  python -m audio_processor whisper --help              # 查看 Whisper 帮助

注意: Qwen 文本生成功能已移动到独立的 qwen 模块
使用方法: python -m qwen.cli --help
        """
    )

    # 添加子命令支持
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 子命令 1： Whisper 转录命令
    whisper_parser = subparsers.add_parser("whisper", help="音频转录")

    # 添加 Whisper 子命令的参数
    whisper_parser.add_argument(
        "audio_files",
        nargs="+",
        help="音频文件路径（支持多个文件）"
    )
    whisper_parser.add_argument(
        "-o", "--output",
        help="输出文件路径（单个文件时）或输出目录（多个文件时）"
    )
    whisper_parser.add_argument(
        "-c", "--config",
        default="whisper",
        help="配置名称 (默认: whisper)"
    )
    whisper_parser.add_argument(
        "-l", "--language",
        help="指定音频语言 (如: zh, en, ja 等，留空自动检测)"
    )
    whisper_parser.add_argument(
        "-f", "--format",
        choices=["json", "txt"],
        help="输出格式"
    )
    whisper_parser.add_argument(
        "--timestamps",
        action="store_true",
        help="包含时间戳信息"
    )
    whisper_parser.add_argument(
        "--batch",
        action="store_true",
        help="批量处理模式（即使只有一个文件）"
    )
    whisper_parser.add_argument(
        "--clear-cache",
        action="store_true",
        help="处理前清理GPU缓存"
    )
    whisper_parser.add_argument(
        "--enable-preprocessing",
        action="store_true",
        help="启用音频预处理（降噪、VAD检测、语音段分割）"
    )
    whisper_parser.add_argument(
        "--disable-preprocessing",
        action="store_true",
        help="禁用音频预处理"
    )
    whisper_parser.set_defaults(func=whisper_cli)



    # 如果未提供任何参数（直接输入了 python -m audio_processor），则打印主帮助信息（epilog）
    if len(sys.argv) == 1: # sys.argv 是一个列表，sys.argv[0] 是脚本名称
        parser.print_help()
        return

    # 向后兼容：如果第一个参数不是子命令且不是帮助，默认为 whisper（即 python -m audio_processor audio.wav）
    if (len(sys.argv) > 1 and
        sys.argv[1] not in ["whisper", "-h", "--help"] and
        not sys.argv[1].startswith("-")):
        # 插入默认命令
        sys.argv.insert(1, "whisper")

    # 解析参数
    args = parser.parse_args()

    # 如果有 func 属性，说明是子命令，则调用子命令的函数
    if hasattr(args, 'func'):
        # 直接传递解析后的参数给子命令函数
        args.func(args)
    else:
        # 否则，打印主帮助信息
        parser.print_help()


if __name__ == "__main__":
    main()
