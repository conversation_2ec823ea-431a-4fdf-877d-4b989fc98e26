# PyTorch Nightly with CUDA 12.8 support
--index-url https://download.pytorch.org/whl/nightly/cu128
torch>=2.8.0.dev
torchvision>=0.22.0.dev
torchaudio>=2.6.0.dev

# Transformers and Hugging Face
transformers>=4.51
huggingface-hub>=0.30.0
tokenizers>=0.21
safetensors>=0.4.3

# Audio processing
librosa>=0.11.0
soundfile>=0.13.0
audioread>=3.0.0

# Utilities
loguru>=0.7.0
pyyaml>=6.0
psutil>=7.0.0
gpustat>=1.1.0

# Scientific computing (dependencies)
numpy>=1.17.0
scipy>=1.6.0
scikit-learn>=1.1.0
numba>=0.51.0

# Other dependencies
tqdm>=4.27
requests>=2.32.0
packaging>=20.0
filelock>=3.16.0

# Llama.cpp Python bindings with CUDA support
llama-cpp-python>=0.3.0

# FastAPI and web server dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
