#!/usr/bin/env python3
"""
阿拉伯语转录测试脚本
使用 large-v3-turbo 模型进行阿拉伯语音频转录，重点测试词级别时间戳功能
"""

import os
import sys
import json
import time
from pathlib import Path

def test_arabic_transcription():
    """测试阿拉伯语转录功能"""
    print("🧪 开始阿拉伯语转录测试")
    print("=" * 60)
    
    # 添加项目路径
    sys.path.append('.')
    
    try:
        from audio_processor.transcribing.whisper_original import OriginalWhisperTranscriber
        print("✅ 成功导入 OriginalWhisperTranscriber")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 检查测试音频文件
    test_audio = "data/samples/过量（二）.mp3"
    if not Path(test_audio).exists():
        print(f"❌ 测试音频文件不存在: {test_audio}")
        return False
    
    print(f"✅ 测试音频文件存在: {test_audio}")
    
    # 创建输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    try:
        print("\n🔧 初始化转录器...")
        start_init = time.time()
        
        # 初始化转录器，禁用预处理以避免soundfile问题
        transcriber = OriginalWhisperTranscriber(enable_preprocessing=False)
        
        init_time = time.time() - start_init
        print(f"✅ 转录器初始化完成，耗时: {init_time:.2f}秒")
        print(f"   模型: {transcriber.model_size}")
        
        print("\n🎯 开始阿拉伯语转录测试...")
        print("   配置: large-v3-turbo + 词级别时间戳")
        
        start_time = time.time()
        
        # 执行转录 - 针对阿拉伯语优化
        result = transcriber.transcribe(
            audio_path=test_audio,
            language='ar',  # 阿拉伯语
            word_timestamps=True,  # 启用词级别时间戳
            temperature=0.0,  # 确定性输出
            initial_prompt="هذا نص باللغة العربية",  # 阿拉伯语初始提示
            condition_on_previous_text=True,
            compression_ratio_threshold=2.4,
            logprob_threshold=-1.0,
            no_speech_threshold=0.6
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"✅ 转录完成，耗时: {processing_time:.2f}秒")
        
        # 分析结果
        text = result.get('text', '')
        chunks = result.get('chunks', [])
        
        print(f"\n📊 转录结果分析:")
        print(f"   转录文本长度: {len(text)} 字符")
        print(f"   转录文本预览: {text[:100]}...")
        print(f"   段数: {len(chunks)}")
        
        # 检查词级别时间戳
        word_count = 0
        has_word_timestamps = False
        
        for i, chunk in enumerate(chunks):
            words = chunk.get('words', [])
            word_count += len(words)
            if words:
                has_word_timestamps = True
                if i == 0:  # 显示第一段的词级别时间戳示例
                    print(f"   第一段词数: {len(words)}")
                    if len(words) > 0:
                        first_word = words[0]
                        print(f"   第一个词示例: {first_word}")
        
        print(f"   总词数: {word_count}")
        
        if has_word_timestamps:
            print("✅ 词级别时间戳功能正常工作")
        else:
            print("❌ 词级别时间戳未找到")
        
        # 保存结果
        output_file = output_dir / "arabic_transcription_test.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 结果已保存到: {output_file}")
        
        # 性能统计
        print(f"\n⚡ 性能统计:")
        print(f"   初始化时间: {init_time:.2f}秒")
        print(f"   转录时间: {processing_time:.2f}秒")
        print(f"   总时间: {init_time + processing_time:.2f}秒")
        
        return has_word_timestamps
        
    except Exception as e:
        print(f"❌ 转录测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_version_compatibility():
    """测试版本兼容性"""
    print("\n🔍 检查版本兼容性...")
    
    try:
        import torch
        import triton
        import whisper
        
        print(f"   PyTorch: {torch.__version__}")
        print(f"   Triton: {triton.__version__}")
        print(f"   Whisper: {whisper.__version__}")
        print(f"   CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"   CUDA版本: {torch.version.cuda}")
            print(f"   GPU数量: {torch.cuda.device_count()}")
            if torch.cuda.device_count() > 0:
                print(f"   GPU名称: {torch.cuda.get_device_name(0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 版本检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 阿拉伯语转录系统测试")
    print("模型: large-v3-turbo")
    print("功能: 词级别时间戳")
    print("=" * 60)
    
    # 检查版本兼容性
    version_ok = test_version_compatibility()
    
    if version_ok:
        # 执行转录测试
        success = test_arabic_transcription()
        
        if success:
            print("\n🎉 阿拉伯语转录测试成功完成！")
            print("✅ 词级别时间戳功能正常工作")
        else:
            print("\n⚠️ 阿拉伯语转录测试失败")
            print("❌ 需要检查Triton兼容性问题")
    else:
        print("\n⚠️ 版本兼容性检查失败")
        print("❌ 需要安装兼容的PyTorch/Triton版本")
