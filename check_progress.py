#!/usr/bin/env python3
"""
检查转录任务进度
"""

import os
import time
from pathlib import Path

def check_progress():
    """检查转录任务进度"""
    print("🔍 检查转录任务进度...")
    
    # 检查输出文件
    output_files = [
        "output/arabic_transcription_test.json",
        "output/word_timestamps_test.json",
        "output/basic_transcription_test.json"
    ]
    
    for output_file in output_files:
        if Path(output_file).exists():
            file_size = Path(output_file).stat().st_size
            mod_time = Path(output_file).stat().st_mtime
            mod_time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(mod_time))
            print(f"✅ 找到输出文件: {output_file}")
            print(f"   大小: {file_size} 字节")
            print(f"   修改时间: {mod_time_str}")
        else:
            print(f"⏳ 等待输出文件: {output_file}")
    
    # 检查日志文件
    log_files = [
        "logs/whisper.log",
        "logs/qwen.log",
        "logs/preprocessing.log"
    ]
    
    print("\n📋 检查日志文件...")
    for log_file in log_files:
        if Path(log_file).exists():
            file_size = Path(log_file).stat().st_size
            print(f"✅ 日志文件: {log_file} ({file_size} 字节)")
            
            # 显示最后几行日志
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"   最后一行: {lines[-1].strip()}")
            except Exception as e:
                print(f"   读取日志失败: {e}")
        else:
            print(f"⏳ 等待日志文件: {log_file}")

if __name__ == "__main__":
    check_progress()
