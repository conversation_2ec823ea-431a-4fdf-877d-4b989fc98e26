"""
设备检测和管理工具模块
提供统一的GPU/CPU设备检测和管理功能
这是SystemMonitor的简化接口层
"""

from typing import Dict, Any, Optional, TYPE_CHECKING
from loguru import logger

if TYPE_CHECKING:
    from loguru import Logger

from .monitor import SystemMonitor


class DeviceManager:
    """设备管理器 - SystemMonitor的简化接口"""

    def __init__(self, module_logger: Optional[Any] = None):
        """初始化设备管理器

        Args:
            module_logger: 模块专用日志记录器（可选）
        """
        self.logger = module_logger or logger

        # 创建一个简化的配置用于SystemMonitor
        config = {
            "monitoring": {
                "enable_memory_monitoring": True,
                "enable_gpu_monitoring": True,
                "log_interval": 10
            },
            "paths": {
                "logs_dir": "logs"
            }
        }

        # 使用SystemMonitor作为底层实现
        self._monitor = SystemMonitor(config)
        # 设置日志记录器
        if module_logger:
            self._monitor.monitor_logger = module_logger

    def get_device_info(self, detailed: bool = True) -> Dict[str, Any]:
        """获取设备信息

        Args:
            detailed: 是否返回详细信息

        Returns:
            设备信息字典
        """
        if detailed:
            # 返回详细的GPU信息
            gpu_info = self._monitor.get_gpu_info()
            summary = self._monitor.get_device_summary()

            return {
                "cuda_available": summary["cuda_available"],
                "gpu_count": summary["gpu_count"],
                "gpu_names": summary["gpu_names"],
                "gpu_details": gpu_info.get("devices", []),
                "total_vram": summary["total_vram"],
                "torch_available": True,  # 如果能运行到这里说明torch可用
                "gpustat_available": True  # SystemMonitor已经处理了gpustat的可用性
            }
        else:
            # 返回简化信息
            summary = self._monitor.get_device_summary()
            return {
                "device": "cuda" if summary["cuda_available"] else "cpu",
                "cuda_available": summary["cuda_available"],
                "gpu_count": summary["gpu_count"]
            }

    def get_preferred_device(self) -> str:
        """获取首选设备

        Returns:
            设备字符串 ("cuda" 或 "cpu")
        """
        return self._monitor.get_preferred_device()

    def log_device_summary(self):
        """记录设备信息摘要"""
        self._monitor.log_device_summary()

    def get_memory_info(self, device_id: int = 0) -> Dict[str, float]:
        """获取指定GPU的内存信息

        Args:
            device_id: GPU设备ID

        Returns:
            内存信息字典 (GB)
        """
        gpu_info = self._monitor.get_gpu_info()
        if not gpu_info["available"] or device_id >= len(gpu_info["devices"]):
            return {}

        device = gpu_info["devices"][device_id]
        return {
            "total": device.get("memory_total", 0),
            "allocated": device.get("memory_allocated", 0),
            "reserved": device.get("memory_reserved", 0),
            "free": device.get("memory_free", 0)
        }

    def clear_gpu_cache(self, device_id: Optional[int] = None):
        """清理GPU缓存

        Args:
            device_id: 指定GPU设备ID，None表示清理所有GPU
        """
        self._monitor.clear_gpu_cache(device_id)

    def clear_gpu_memory(self, device_id: int = 0) -> Dict[str, Any]:
        """清理GPU显存

        Args:
            device_id: GPU设备ID

        Returns:
            清理结果信息
        """
        return self._monitor.clear_gpu_memory(device_id)

    def get_real_gpu_memory(self, device_id: int = 0) -> float:
        """获取真实的GPU显存使用情况（MB）

        Args:
            device_id: GPU设备ID

        Returns:
            GPU显存使用量（MB）
        """
        return self._monitor.get_real_gpu_memory(device_id)

    def refresh_device_info(self):
        """刷新设备信息"""
        # SystemMonitor会自动获取最新信息，这里不需要特别处理
        pass


# 便捷函数
def get_device_manager(module_logger: Optional[Any] = None) -> DeviceManager:
    """获取设备管理器实例

    Args:
        module_logger: 模块专用日志记录器

    Returns:
        设备管理器实例
    """
    return DeviceManager(module_logger)


def get_preferred_device(module_logger: Optional[Any] = None) -> str:
    """获取首选设备

    Args:
        module_logger: 模块专用日志记录器

    Returns:
        设备字符串 ("cuda" 或 "cpu")
    """
    manager = DeviceManager(module_logger)
    return manager.get_preferred_device()


def get_device_info(detailed: bool = True, module_logger: Optional[Any] = None) -> Dict[str, Any]:
    """获取设备信息

    Args:
        detailed: 是否返回详细信息
        module_logger: 模块专用日志记录器

    Returns:
        设备信息字典
    """
    manager = DeviceManager(module_logger)
    return manager.get_device_info(detailed)


def log_device_summary(module_logger: Optional[Any] = None):
    """记录设备信息摘要

    Args:
        module_logger: 模块专用日志记录器
    """
    manager = DeviceManager(module_logger)
    manager.log_device_summary()
