"""
输入输出工具模块
"""

import json
from pathlib import Path
from typing import Dict, Any

from loguru import logger


def save_transcription_result(result: Dict[str, Any], output_path: str, output_format: str = "json"):
    """保存转录结果
    
    Args:
        result: 转录结果
        output_path: 输出文件路径
        output_format: 输出格式 (json, txt)
    """
    try:
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        if output_format == "json":
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
        elif output_format == "txt":
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(result["text"])
        else:
            logger.warning(f"不支持的输出格式: {output_format}，使用JSON格式")
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
        
        logger.info(f"结果已保存到: {output_path}")
        
    except Exception as e:
        logger.error(f"保存结果失败: {e}")
        raise


def load_json_file(file_path: str) -> Dict[str, Any]:
    """加载JSON文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        JSON数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载JSON文件失败: {e}")
        raise


def save_json_file(data: Dict[str, Any], file_path: str):
    """保存JSON文件
    
    Args:
        data: 要保存的数据
        file_path: 文件路径
    """
    try:
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"JSON文件已保存到: {file_path}")
        
    except Exception as e:
        logger.error(f"保存JSON文件失败: {e}")
        raise
