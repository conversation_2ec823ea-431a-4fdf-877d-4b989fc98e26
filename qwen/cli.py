"""
Qwen API 服务器命令行工具
"""

import sys
import argparse
from pathlib import Path

from loguru import logger
from .api.server import run_server


def setup_logging(verbose: bool = False):
    """设置日志"""
    logger.remove()
    level = "DEBUG" if verbose else "INFO"
    logger.add(sys.stderr, level=level, format="{time:HH:mm:ss} | {level} | {message}")


def qwen_cli(args=None):
    """Qwen API 服务器命令行入口"""
    # 如果没有传递参数，则自己解析
    if args is None:
        parser = argparse.ArgumentParser(
            description="Qwen Q5_0 API 服务器",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
  python -m audio_processor qwen                       # 启动服务器（默认端口8000）
  python -m audio_processor qwen --port 9000           # 指定端口
  python -m audio_processor qwen --reload              # 开发模式（自动重载）
  python -m audio_processor qwen --verbose             # 详细日志

API 接口:
  POST /api/v1/generate                                 # 文本生成
  GET  /api/v1/model/info                               # 模型信息
  GET  /api/v1/health                                   # 健康检查

API 文档:
  http://localhost:8000/docs                            # Swagger UI
  http://localhost:8000/redoc                           # ReDoc
            """
        )

        parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址 (默认: 0.0.0.0)")
        parser.add_argument("--port", type=int, default=8000, help="服务器端口 (默认: 8000)")
        parser.add_argument("--reload", action="store_true", help="启用自动重载（开发模式）")
        parser.add_argument("--log-level", default="info",
                           choices=["debug", "info", "warning", "error"],
                           help="日志级别 (默认: info)")
        parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")

        args = parser.parse_args()

    # 为传递的参数设置默认值（如果没有的话）
    if not hasattr(args, 'reload'):
        args.reload = False
    if not hasattr(args, 'log_level'):
        args.log_level = "info"
    if not hasattr(args, 'verbose'):
        args.verbose = False

    # 设置日志
    setup_logging(args.verbose)

    try:
        print("🚀 启动 Qwen Q5_0 API 服务器...")
        print(f"📡 服务器地址: http://{args.host}:{args.port}")
        print(f"📚 API 文档: http://{args.host}:{args.port}/docs")
        print(f"🔍 ReDoc 文档: http://{args.host}:{args.port}/redoc")
        print("=" * 60)

        run_server(
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        print(f"\n❌ 服务器启动失败: {e}")
        print("\n🔧 故障排除:")
        print("1. 确保已安装 FastAPI 和 uvicorn: pip install fastapi uvicorn")
        print("2. 确保已安装 llama-cpp-python: ./scripts/install_llama_cpp_python.sh")
        print("3. 确保模型文件存在: audio_processor/models/qwen/Qwen3-32B-Q5_0.gguf")
        print("4. 检查端口是否被占用: netstat -tulpn | grep :8000")
        sys.exit(1)


if __name__ == "__main__":
    qwen_cli()
