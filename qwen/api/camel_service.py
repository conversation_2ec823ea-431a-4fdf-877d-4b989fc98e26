"""
CAMeL Tools API服务
用于在独立环境中运行camel-tools功能
"""

import subprocess
import json
import tempfile
from pathlib import Path
from typing import Dict, Any, List
from loguru import logger

class CamelToolsService:
    """CAMeL Tools服务类，通过独立进程调用camel-tools功能"""

    def __init__(self, camel_env_name: str = "camel_tools_env"):
        """
        初始化CAMeL Tools服务

        Args:
            camel_env_name: camel-tools conda环境名称
        """
        self.camel_env_name = camel_env_name
        self.conda_base = self._get_conda_base()

    def _get_conda_base(self) -> str:
        """获取conda基础路径"""
        try:
            result = subprocess.run(
                ["conda", "info", "--base"],
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError:
            raise RuntimeError("无法获取conda基础路径，请确保conda已安装")

    def _run_camel_command(self, script_content: str) -> Dict[str, Any]:
        """
        在camel-tools环境中运行Python脚本

        Args:
            script_content: 要执行的Python脚本内容

        Returns:
            执行结果字典
        """
        # 创建临时脚本文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(script_content)
            script_path = f.name

        try:
            # 构建命令
            activate_cmd = f"source {self.conda_base}/etc/profile.d/conda.sh"
            conda_cmd = f"conda activate {self.camel_env_name}"
            python_cmd = f"python {script_path}"
            full_cmd = f"{activate_cmd} && {conda_cmd} && {python_cmd}"

            # 执行命令
            result = subprocess.run(
                full_cmd,
                shell=True,
                capture_output=True,
                text=True,
                executable="/bin/bash"
            )

            if result.returncode != 0:
                logger.error(f"CAMeL Tools执行失败: {result.stderr}")
                return {
                    "success": False,
                    "error": result.stderr,
                    "stdout": result.stdout
                }

            # 尝试解析JSON输出
            try:
                output_data = json.loads(result.stdout)
                return {
                    "success": True,
                    "data": output_data
                }
            except json.JSONDecodeError:
                return {
                    "success": True,
                    "data": result.stdout.strip()
                }

        finally:
            # 清理临时文件
            Path(script_path).unlink(missing_ok=True)

    def tokenize_arabic(self, text: str) -> Dict[str, Any]:
        """
        阿拉伯语分词

        Args:
            text: 要分词的阿拉伯语文本

        Returns:
            分词结果
        """
        script = f'''
import json
from camel_tools.tokenizers.word import simple_word_tokenize

text = """{text}"""
tokens = simple_word_tokenize(text)
result = {{
    "tokens": tokens,
    "count": len(tokens)
}}
print(json.dumps(result, ensure_ascii=False))
'''
        return self._run_camel_command(script)

    def morphological_analysis(self, text: str) -> Dict[str, Any]:
        """
        阿拉伯语形态学分析

        Args:
            text: 要分析的阿拉伯语文本

        Returns:
            形态学分析结果
        """
        script = f'''
import json
from camel_tools.morphology.database import MorphologyDB
from camel_tools.morphology.analyzer import Analyzer

# 初始化形态学分析器
db = MorphologyDB.builtin_db()
analyzer = Analyzer(db)

text = """{text}"""
words = text.split()
results = []

for word in words:
    analyses = analyzer.analyze(word)
    word_result = {{
        "word": word,
        "analyses": [{{
            "lemma": analysis.get("lemma", ""),
            "pos": analysis.get("pos", ""),
            "features": analysis.get("features", "")
        }} for analysis in analyses[:3]]  # 只取前3个分析结果
    }}
    results.append(word_result)

result = {{
    "text": text,
    "morphological_analysis": results
}}
print(json.dumps(result, ensure_ascii=False))
'''
        return self._run_camel_command(script)

    def transliterate(self, text: str, scheme: str = "ar2bw") -> Dict[str, Any]:
        """
        阿拉伯语转写

        Args:
            text: 要转写的文本
            scheme: 转写方案 (ar2bw, bw2ar, ar2safebw等)

        Returns:
            转写结果
        """
        script = f'''
import json
try:
    from camel_tools.utils.transliterate import Transliterator
    transliterator = Transliterator("{scheme}")
    text = """{text}"""
    transliterated = transliterator.transliterate(text)

    result = {{
        "success": True,
        "original": text,
        "transliterated": transliterated,
        "scheme": "{scheme}"
    }}
except Exception as e:
    result = {{
        "success": False,
        "error": str(e),
        "original": """{text}""",
        "scheme": "{scheme}"
    }}
print(json.dumps(result, ensure_ascii=False))
'''
        return self._run_camel_command(script)

    def check_environment(self) -> Dict[str, Any]:
        """检查camel-tools环境是否正确设置"""
        script = '''
import json
import sys

try:
    import camel_tools
    import transformers

    result = {
        "success": True,
        "camel_tools_version": camel_tools.__version__,
        "transformers_version": transformers.__version__,
        "python_version": sys.version
    }
except ImportError as e:
    result = {
        "success": False,
        "error": str(e)
    }

print(json.dumps(result))
'''
        return self._run_camel_command(script)
