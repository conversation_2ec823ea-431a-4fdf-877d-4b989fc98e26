"""
API 数据模型定义
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class GenerateTextRequest(BaseModel):
    """文本生成请求模型"""

    prompt: str = Field(..., description="输入提示文本", min_length=1)
    system_prompt: Optional[str] = Field(None, description="系统提示词")
    max_tokens: Optional[int] = Field(None, description="最大生成token数", ge=1, le=100000)
    temperature: Optional[float] = Field(None, description="温度参数", ge=0.0, le=2.0)
    top_p: Optional[float] = Field(None, description="top_p参数", ge=0.0, le=1.0)
    top_k: Optional[int] = Field(None, description="top_k参数", ge=1, le=100)
    min_p: Optional[float] = Field(None, description="min_p参数", ge=0.0, le=1.0)
    repeat_penalty: Optional[float] = Field(None, description="重复惩罚", ge=0.0, le=2.0)
    presence_penalty: Optional[float] = Field(None, description="存在惩罚", ge=0.0, le=2.0)
    stop: Optional[List[str]] = Field(None, description="停止词列表")
    task_type: Optional[str] = Field(None, description="任务类型", pattern="^(math|programming|multiple_choice)$")
    enable_thinking: Optional[bool] = Field(None, description="是否启用思维模式")

    # 显存管理选项
    clear_cache_after: Optional[bool] = Field(False, description="生成完成后是否清理GPU缓存")
    unload_model_after: Optional[bool] = Field(False, description="生成完成后是否卸载模型")
    auto_reload_model: Optional[bool] = Field(True, description="如果模型被卸载，下次请求时是否自动重新加载")


class GenerateTextResponse(BaseModel):
    """文本生成响应模型"""

    generated_text: str = Field(..., description="生成的文本")
    prompt: str = Field(..., description="原始提示")
    system_prompt: Optional[str] = Field(None, description="系统提示词")
    final_prompt: str = Field(..., description="最终使用的提示")
    processing_time: float = Field(..., description="处理时间（秒）")
    model_path: str = Field(..., description="模型路径")
    generation_params: Dict[str, Any] = Field(..., description="生成参数")
    usage: Dict[str, Any] = Field(..., description="使用统计")
    timestamp: str = Field(..., description="时间戳")
    thinking_mode: bool = Field(..., description="是否使用思维模式")
    task_type: Optional[str] = Field(None, description="任务类型")

    # 显存管理结果
    memory_management: Optional[Dict[str, Any]] = Field(None, description="显存管理操作结果")


class ModelInfoResponse(BaseModel):
    """模型信息响应模型"""

    model_path: str = Field(..., description="模型路径")
    context_length: int = Field(..., description="上下文长度")
    gpu_layers: int = Field(..., description="GPU层数")
    device_info: Dict[str, Any] = Field(..., description="设备信息")
    loaded: bool = Field(..., description="是否已加载")


class ErrorResponse(BaseModel):
    """错误响应模型"""

    error: str = Field(..., description="错误信息")
    detail: Optional[str] = Field(None, description="详细错误信息")
    timestamp: str = Field(..., description="时间戳")


class HealthResponse(BaseModel):
    """健康检查响应模型"""

    status: str = Field(..., description="服务状态")
    version: str = Field(..., description="版本号")
    model_loaded: bool = Field(..., description="模型是否已加载")
    timestamp: str = Field(..., description="时间戳")
