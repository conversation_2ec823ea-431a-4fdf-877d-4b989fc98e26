"""
FastAPI 服务器主文件
"""

import time
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from loguru import logger

from .routes import router
from .models import ErrorResponse


def create_app() -> FastAPI:
    """创建 FastAPI 应用"""
    
    app = FastAPI(
        title="Audio Processor API",
        description="基于 Qwen Q5_0 的文本生成 API 服务",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 添加 CORS 中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 在生产环境中应该限制具体的域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 包含路由
    app.include_router(router, prefix="/api/v1")
    
    # 全局异常处理器
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        logger.error(f"未处理的异常: {exc}")
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                error="内部服务器错误",
                detail=str(exc),
                timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
            ).dict()
        )
    
    # 根路径
    @app.get("/")
    async def root():
        return {
            "message": "Audio Processor API",
            "version": "1.0.0",
            "docs": "/docs",
            "health": "/api/v1/health"
        }
    
    return app


def run_server(
    host: str = "0.0.0.0",
    port: int = 8000,
    reload: bool = False,
    log_level: str = "info"
):
    """运行服务器"""
    
    logger.info(f"启动 API 服务器: http://{host}:{port}")
    logger.info(f"API 文档: http://{host}:{port}/docs")
    
    app = create_app()
    
    uvicorn.run(
        app,
        host=host,
        port=port,
        reload=reload,
        log_level=log_level,
        access_log=True
    )


if __name__ == "__main__":
    run_server()
