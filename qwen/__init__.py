"""
Qwen 模块 - 独立的文本生成AI模型
完全与音频转录逻辑隔离
"""

# 可选导入 - Qwen 处理器（需要 llama-cpp-python）
try:
    from .processor import QwenProcessor
    _qwen_available = True
    _import_error = None
except ImportError as e:
    QwenProcessor = None
    _qwen_available = False
    _import_error = str(e)

__all__ = []

# 只有在 Qwen 可用时才添加到 __all__
if _qwen_available:
    __all__.append("QwenProcessor")

def is_available():
    """检查 Qwen 模块是否可用"""
    return _qwen_available

def get_import_error():
    """获取导入错误信息"""
    if _qwen_available:
        return None
    return _import_error
