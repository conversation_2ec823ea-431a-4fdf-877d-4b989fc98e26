"""
配置管理模块
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from loguru import logger


class ConfigManager:
    """配置管理器"""

    def __init__(self, config_dir: str = "configs"): # 使用程序运行目录下的"configs"目录
        """初始化配置管理器

        Args:
            config_dir: 配置文件目录
        """

        self.config_dir = Path(config_dir) # 当Path接受一个不包含任何路径分隔符的字符串的时候，他会被解释成一个程序运行目录的路径
        self._configs = {}

    def load_config(self, config_name: str, config_file: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
        """加载配置文件

        Args:
            config_name: 配置目录的名称
            config_file: 配置文件路径（可选）

        Returns:
            配置字典

        Raises:
            FileNotFoundError: 当配置文件不存在时
            ValueError: 当配置文件格式错误时
        """
        if config_name in self._configs:
            return self._configs[config_name]

        if config_file is None:
            config_file = self.config_dir / f"{config_name}.yaml"
        else:
            config_file = Path(config_file)

        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            if config is None:
                raise ValueError(f"配置文件为空或格式错误: {config_file}")

            self._configs[config_name] = config
            logger.info(f"加载配置文件: {config_file}")
            return config

        except yaml.YAMLError as e:
            raise ValueError(f"配置文件YAML格式错误: {config_file}, 错误: {e}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {config_file}, 错误: {e}")
            raise

    def get_config(self, config_name: str) -> Dict[str, Any]:
        """获取已加载的配置

        Args:
            config_name: 配置名称

        Returns:
            配置字典
        """
        if config_name not in self._configs:
            return self.load_config(config_name)
        return self._configs[config_name]

    def update_config(self, config_name: str, updates: Dict[str, Any]):
        """更新配置

        Args:
            config_name: 配置名称
            updates: 更新的配置项
        """
        if config_name not in self._configs:
            self.load_config(config_name)

        self._configs[config_name].update(updates)
        logger.info(f"更新配置: {config_name}")

    def save_config(self, config_name: str, config_file: Optional[Union[str, Path]] = None):
        """保存配置到文件

        Args:
            config_name: 配置名称
            config_file: 配置文件路径（可选）
        """
        if config_name not in self._configs:
            logger.error(f"配置不存在: {config_name}")
            return

        if config_file is None:
            config_file = self.config_dir / f"{config_name}.yaml"
        else:
            config_file = Path(config_file)

        try:
            config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self._configs[config_name], f, default_flow_style=False, allow_unicode=True)

            logger.info(f"保存配置文件: {config_file}")

        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")

    def get_config_file_path(self, config_name: str) -> Path:
        """获取配置文件路径

        Args:
            config_name: 配置名称

        Returns:
            配置文件路径
        """
        return self.config_dir / f"{config_name}.yaml"

    def config_exists(self, config_name: str) -> bool:
        """检查配置文件是否存在

        Args:
            config_name: 配置名称

        Returns:
            配置文件是否存在
        """
        return self.get_config_file_path(config_name).exists()

    def list_available_configs(self) -> list[str]:
        """列出所有可用的配置文件

        Returns:
            配置文件名称列表（不包含.yaml扩展名）
        """
        if not self.config_dir.exists():
            return []

        config_files = []
        for file_path in self.config_dir.glob("*.yaml"):
            config_files.append(file_path.stem)

        return sorted(config_files)




# 全局配置管理器实例
config_manager = ConfigManager()
