#!/usr/bin/env python3
"""
Qwen 模块自动环境切换启动脚本 (Python 版本)
自动切换到 audio_processor_env 环境并启动 Qwen API 服务器
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def print_colored(message, color=""):
    """打印带颜色的消息"""
    colors = {
        "red": "\033[0;31m",
        "green": "\033[0;32m", 
        "yellow": "\033[1;33m",
        "blue": "\033[0;34m",
        "reset": "\033[0m"
    }
    
    if color in colors:
        print(f"{colors[color]}{message}{colors['reset']}")
    else:
        print(message)

def print_info(message):
    print_colored(f"[INFO] {message}", "blue")

def print_success(message):
    print_colored(f"[SUCCESS] {message}", "green")

def print_warning(message):
    print_colored(f"[WARNING] {message}", "yellow")

def print_error(message):
    print_colored(f"[ERROR] {message}", "red")

def check_conda():
    """检查 conda 是否可用"""
    try:
        result = subprocess.run(["conda", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print_success("conda 已找到")
            return True
        else:
            print_error("conda 未找到，请确保已安装 Anaconda 或 Miniconda")
            return False
    except FileNotFoundError:
        print_error("conda 未找到，请确保已安装 Anaconda 或 Miniconda")
        return False

def check_environment():
    """检查环境是否存在"""
    env_name = "audio_processor_env"
    
    try:
        result = subprocess.run(["conda", "env", "list"], capture_output=True, text=True)
        if result.returncode == 0:
            env_lines = result.stdout.split('\n')
            for line in env_lines:
                if line.strip().startswith(env_name + " "):
                    print_success(f"环境 {env_name} 已存在")
                    return True
            
            print_error(f"环境 {env_name} 不存在")
            print_info("请先创建环境：")
            print_info("  conda create -n audio_processor_env python=3.11 -y")
            print_info("  conda activate audio_processor_env")
            print_info("  pip install llama-cpp-python torch fastapi uvicorn")
            return False
        else:
            print_error("无法检查 conda 环境")
            return False
    except Exception as e:
        print_error(f"检查环境时出错: {e}")
        return False

def check_dependencies():
    """检查必要的包是否已安装"""
    env_name = "audio_processor_env"
    
    print_info(f"检查环境 {env_name} 中的依赖...")
    
    packages = ["llama_cpp", "torch", "fastapi", "uvicorn"]
    missing_packages = []
    
    for package in packages:
        try:
            # 在指定环境中检查包
            cmd = f"conda run -n {env_name} python -c \"import {package}\""
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode != 0:
                missing_packages.append(package)
        except Exception:
            missing_packages.append(package)
    
    if missing_packages:
        print_error(f"缺少以下包: {', '.join(missing_packages)}")
        print_info("请安装缺少的包：")
        print_info(f"  conda activate {env_name}")
        for package in missing_packages:
            if package == "llama_cpp":
                print_info("  pip install llama-cpp-python")
            else:
                print_info(f"  pip install {package}")
        return False
    
    print_success("所有依赖已安装")
    return True

def check_model_file():
    """检查模型文件是否存在"""
    model_file = Path("Qwen3-32B-Q5_0.gguf")
    
    if model_file.exists():
        print_success(f"模型文件 {model_file.name} 已找到")
    else:
        print_warning(f"模型文件 {model_file.name} 未找到")
        print_info("请确保模型文件在 qwen 目录中")
        print_info("如果没有模型文件，Qwen 服务器可能无法正常启动")

def start_qwen_server(args):
    """启动 Qwen 服务器"""
    env_name = "audio_processor_env"
    
    print_info(f"切换到环境 {env_name}...")
    
    # 构建命令
    cmd_parts = ["conda", "run", "-n", env_name, "python", "-m", "qwen.cli"]
    
    # 添加参数
    if args.host:
        cmd_parts.extend(["--host", args.host])
    if args.port:
        cmd_parts.extend(["--port", str(args.port)])
    if args.reload:
        cmd_parts.append("--reload")
    if args.log_level:
        cmd_parts.extend(["--log-level", args.log_level])
    if args.verbose:
        cmd_parts.append("--verbose")
    
    # 设置环境变量
    env = os.environ.copy()
    project_root = str(Path(__file__).parent.parent)
    env["PYTHONPATH"] = f"{project_root}:{env.get('PYTHONPATH', '')}"
    
    print_success(f"已切换到环境 {env_name}")
    print_info(f"PYTHONPATH: {env['PYTHONPATH']}")
    print_info("启动 Qwen API 服务器...")
    
    try:
        # 运行命令
        subprocess.run(cmd_parts, env=env, check=True)
    except subprocess.CalledProcessError as e:
        print_error(f"启动 Qwen 服务器失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_info("\n服务器已停止")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Qwen 模块自动环境切换启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python run_qwen.py                           # 使用默认设置启动
  python run_qwen.py --port 9000              # 在端口 9000 启动
  python run_qwen.py --host 127.0.0.1 --reload # 本地开发模式

环境要求:
  - conda 环境: audio_processor_env
  - Python 包: llama-cpp-python, torch, fastapi, uvicorn
  - 模型文件: Qwen3-32B-Q5_0.gguf (可选)
        """
    )
    
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址 (默认: 0.0.0.0)")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口 (默认: 8000)")
    parser.add_argument("--reload", action="store_true", help="启用自动重载（开发模式）")
    parser.add_argument("--log-level", default="info", 
                       choices=["debug", "info", "warning", "error"],
                       help="日志级别 (默认: info)")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    print_info("🚀 Qwen 模块自动环境切换启动脚本")
    print_info("=" * 50)
    
    # 执行检查
    checks = [
        check_conda(),
        check_environment(),
        check_dependencies()
    ]
    
    check_model_file()  # 这个是可选的，不影响启动
    
    if not all(checks):
        print_error("检查失败，无法启动 Qwen 服务器")
        sys.exit(1)
    
    print_info("=" * 50)
    print_success("所有检查通过，准备启动 Qwen 服务器...")
    
    # 启动服务器
    start_qwen_server(args)

if __name__ == "__main__":
    main()
