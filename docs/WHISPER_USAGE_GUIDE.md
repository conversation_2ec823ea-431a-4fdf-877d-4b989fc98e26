# Whisper 音频转录使用指南

本指南详细介绍如何使用 Audio Processor 中的 Whisper 音频转录功能。

## 概述

Whisper 模块基于 OpenAI 的 Whisper Large V3 Turbo 模型，提供高质量的音频转录服务，支持多种音频格式和语言。

## 特性

- 🚀 **高性能**: 使用 PyTorch + CUDA 12.8，支持 GPU 加速
- 🎯 **最新模型**: 基于 `openai/whisper-large-v3-turbo` 模型
- 🎵 **多格式支持**: 支持 WAV、MP3、FLAC、M4A、OGG 等音频格式
- ⏱️ **时间戳支持**: 可选择包含详细时间戳信息
- 📦 **批量处理**: 支持批量转录多个音频文件
- 🌍 **多语言**: 支持 100+ 种语言自动检测和转录

## 配置文件

Whisper 模块的配置文件位于 `configs/whisper.yaml`：

```yaml
# 模型配置
model:
  name: "openai/whisper-large-v3-turbo"
  device: "cuda"
  torch_dtype: "float16"

# 音频处理配置
audio:
  sample_rate: 16000
  max_duration: 30
  supported_formats: [".wav", ".mp3", ".flac", ".m4a", ".ogg"]

# 输出配置
output:
  format: "json"  # json, txt, srt
  include_timestamps: true
  include_confidence: false

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file: "logs/whisper.log"
  rotation: "10 MB"
  retention: "7 days"

# 监控配置
monitoring:
  enable_memory_monitoring: true
  enable_gpu_monitoring: true
  log_interval: 10
```

## 使用方法

### 1. 命令行使用

#### 基本转录
```bash
# 转录单个音频文件
python -m audio_processor whisper audio.wav

# 指定输出文件
python -m audio_processor whisper audio.wav -o result.json

# 指定语言（可选，留空自动检测）
python -m audio_processor whisper audio.wav -l zh
```

#### 批量处理
```bash
# 批量转录多个文件
python -m audio_processor whisper *.wav --batch

# 批量处理指定目录
python -m audio_processor whisper data/audio/*.mp3 --batch
```

#### 高级选项
```bash
# 包含时间戳信息
python -m audio_processor whisper audio.wav --timestamps

# 指定输出格式
python -m audio_processor whisper audio.wav -f txt

# 使用自定义配置文件
python -m audio_processor whisper audio.wav -c my_whisper_config.yaml

# 处理前清理GPU缓存
python -m audio_processor whisper audio.wav --clear-cache
```

#### 转录参数控制
```bash
# 指定任务类型（转录或翻译）
python -m audio_processor whisper audio.wav --task transcribe
python -m audio_processor whisper audio.wav --task translate

# 控制生成随机性（温度参数）
python -m audio_processor whisper audio.wav --temperature 0.0  # 确定性输出
python -m audio_processor whisper audio.wav --temperature 0.6  # 平衡模式
python -m audio_processor whisper audio.wav --temperature 1.0  # 高随机性

# 设置最大生成长度
python -m audio_processor whisper audio.wav --max-tokens 1000

# 使用beam search提高质量
python -m audio_processor whisper audio.wav --num-beams 5

# 调整无语音检测阈值
python -m audio_processor whisper audio.wav --no-speech-threshold 0.8

# 生成词级时间戳
python -m audio_processor whisper audio.wav --word-timestamps
python -m audio_processor whisper audio.wav --timestamp-granularity word

# 使用初始提示词
python -m audio_processor whisper audio.wav --initial-prompt "以下是一段会议录音："

# 组合多个参数
python -m audio_processor whisper audio.wav \
    --language zh \
    --task transcribe \
    --temperature 0.2 \
    --max-tokens 512 \
    --num-beams 3 \
    --timestamp-granularity word \
    --initial-prompt "这是一段技术讨论："
```

### 2. Python API 使用

#### 基本使用
```python
from audio_processor.transcribing.transcriber import WhisperTranscriber

# 创建转录器
transcriber = WhisperTranscriber("whisper")

# 转录单个文件
result = transcriber.transcribe("audio.wav")
print(result["text"])

# 获取详细信息
print(f"处理时间: {result['processing_time']:.2f}秒")
```

#### 使用转录参数
```python
# 指定语言和任务类型
result = transcriber.transcribe(
    "audio.wav",
    language="zh",           # 指定中文
    task="transcribe"        # 转录任务
)

# 控制生成质量
result = transcriber.transcribe(
    "audio.wav",
    temperature=0.2,         # 低随机性，更确定的输出
    max_new_tokens=1000,     # 最大生成长度
    num_beams=3,             # 使用beam search提高质量
    no_speech_threshold=0.8  # 提高无语音检测阈值
)

# 生成词级时间戳
result = transcriber.transcribe(
    "audio.wav",
    timestamp_granularity="word"  # 词级时间戳
)

# 翻译为英文
result = transcriber.transcribe(
    "chinese_audio.wav",
    task="translate",        # 翻译任务
    language="zh"            # 源语言为中文
)

# 使用初始提示词
result = transcriber.transcribe(
    "meeting_audio.wav",
    initial_prompt="以下是一段会议录音，包含技术讨论："
)

# 使用温度回退策略（在配置文件中启用）
transcriber.update_config({
    "transcription": {
        "temperature_fallback": {
            "enable": True,
            "temperatures": [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]
        }
    }
})
result = transcriber.transcribe("difficult_audio.wav")
```

#### 批量处理
```python
# 批量转录
audio_files = ["audio1.wav", "audio2.mp3", "audio3.flac"]
results = transcriber.transcribe_batch(audio_files)

for result in results:
    print(f"文件: {result['audio_file']}")
    print(f"文本: {result['text']}")
    print("---")
```

#### 保存结果
```python
# 保存单个结果
transcriber.save_result(result, "output.json")

# 保存批量结果
transcriber.save_batch_results(results, "batch_output.json")
```

### 3. 配置自定义

#### 修改模型设置
```python
# 更新配置
transcriber.update_config({
    "model": {
        "torch_dtype": "float32"  # 提高精度但增加内存使用
    },
    "output": {
        "include_confidence": True  # 包含置信度分数
    }
})
```

#### 获取配置值
```python
# 获取特定配置
model_name = transcriber.get_config_value("model.name")
sample_rate = transcriber.get_config_value("audio.sample_rate", 16000)
```

## 输出格式

### JSON 格式（默认）
```json
{
  "text": "转录的文本内容",
  "audio_file": "audio.wav",
  "processing_time": 2.34,
  "audio_duration": 10.5,
  "model": "openai/whisper-large-v3-turbo",
  "timestamp": "2024-01-01 12:00:00",
  "chunks": [
    {
      "text": "第一段文本",
      "timestamp": [0.0, 3.5]
    },
    {
      "text": "第二段文本",
      "timestamp": [3.5, 7.2]
    }
  ]
}
```

### TXT 格式
```
转录的文本内容
```

### SRT 字幕格式
```
1
00:00:00,000 --> 00:00:03,500
第一段文本

2
00:00:03,500 --> 00:00:07,200
第二段文本
```

## 性能优化

### GPU 内存优化
```python
# 使用 float16 减少内存使用
transcriber.update_config({
    "model": {
        "torch_dtype": "float16"
    }
})

# 清理 GPU 缓存
import torch
torch.cuda.empty_cache()
```

### 批量处理优化
```python
# 设置合适的批量大小
batch_size = 4  # 根据GPU内存调整
for i in range(0, len(audio_files), batch_size):
    batch = audio_files[i:i+batch_size]
    results = transcriber.transcribe_batch(batch)
```

## 监控和日志

### 系统监控
```python
# 启用详细监控
transcriber.update_config({
    "monitoring": {
        "enable_memory_monitoring": True,
        "enable_gpu_monitoring": True,
        "log_interval": 5  # 每5秒记录一次
    }
})
```

### 日志配置
```python
# 调整日志级别
transcriber.update_config({
    "logging": {
        "level": "DEBUG",  # 更详细的日志
        "file": "logs/whisper_debug.log"
    }
})
```

## 故障排除

### 常见问题

#### 1. CUDA 不可用
```bash
# 检查 CUDA 安装
nvidia-smi
python -c "import torch; print(torch.cuda.is_available())"
```

#### 2. 显存不足
- 调整 `torch_dtype` 为 `float16`
- 减少批量处理的文件数量
- 使用 `torch.cuda.empty_cache()` 清理缓存

#### 3. 音频格式不支持
```python
# 检查支持的格式
supported = transcriber.get_config_value("audio.supported_formats")
print(f"支持的格式: {supported}")
```

#### 4. 模型下载失败
```bash
# 设置 HuggingFace 镜像
export HF_ENDPOINT=https://hf-mirror.com
```

### 错误代码说明

- `FileNotFoundError`: 音频文件不存在
- `RuntimeError`: GPU 内存不足或 CUDA 错误
- `ValueError`: 音频格式不支持或参数错误

## 最佳实践

1. **预处理音频**: 确保音频质量良好，采样率适当
2. **批量处理**: 大量文件使用批量模式提高效率
3. **内存管理**: 定期清理 GPU 缓存，监控内存使用
4. **配置优化**: 根据硬件配置调整模型参数
5. **错误处理**: 实现适当的异常处理和重试机制

## 示例脚本

完整的使用示例请参考：
- `scripts/test_new_architecture.py` - 基础功能测试
- `tests/test_installation.py` - 安装验证测试

## 相关文档

- [项目总览](../README.md)
- [Qwen 使用指南](QWEN_USAGE_GUIDE.md)
- [配置文件说明](../configs/)
