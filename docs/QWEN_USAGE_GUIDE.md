# Qwen Q5_0 完整使用指南

## 🚀 快速开始

### 1. 安装依赖
```bash
# 安装 API 依赖
./scripts/install_api_dependencies.sh

# 安装 llama-cpp-python (支持RTX5090)
./scripts/install_llama_cpp_python.sh

# 验证安装
python scripts/test_qwen_integration.py
```

### 2. 启动 API 服务器
```bash
# 基本启动
python qwen_api_server.py

# 指定端口
python qwen_api_server.py --port 9000

# 开发模式（自动重载）
python qwen_api_server.py --reload

# 详细日志
python qwen_api_server.py --verbose
```

服务器启动后，可以访问：
- API 文档: http://localhost:8000/docs
- ReDoc 文档: http://localhost:8000/redoc
- 健康检查: http://localhost:8000/api/v1/health

## � 程序内 API 调用

### Python API 调用

```python
import requests

class QwenAPIClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.api_base = f"{self.base_url}/api/v1"

    def generate_text(self, prompt, **kwargs):
        data = {"prompt": prompt, **kwargs}
        response = requests.post(f"{self.api_base}/generate", json=data)
        response.raise_for_status()
        return response.json()

    def get_model_info(self):
        response = requests.get(f"{self.api_base}/model/info")
        response.raise_for_status()
        return response.json()

# 使用示例
client = QwenAPIClient()

# 基本生成
result = client.generate_text("你好", max_tokens=500)
print(result["generated_text"])

# 思维模式（重要：使用足够的 tokens）
result = client.generate_text(
    prompt="解释机器学习",
    system_prompt="你是一个AI专家",
    max_tokens=2000,
    temperature=0.7,
    enable_thinking=True
)
print(result["generated_text"])
```

## ⚙️ 参数说明

### 基本参数

- `--max-tokens`: 最大生成token数
  - **重要**: 思维模式需要足够的 tokens 才能正常工作
  - 推荐值: 基本问题 500+，复杂问题 1000+，数学/编程问题 2000+
- `--temperature`: 温度参数 (0.0-2.0)
- `--top-p`: top_p参数 (0.0-1.0)
- `--top-k`: top_k参数 (1-100)
- `--repeat-penalty`: 重复惩罚 (0.0-2.0)
- `--presence-penalty`: 存在惩罚 (0.0-2.0)

### 模式控制

- `--thinking`: 启用思维模式
- `--no-thinking`: 禁用思维模式
- `--task-type`: 任务类型 (math/programming/multiple_choice)
- `--system`: 设置系统提示词

### 官方最佳实践

- **思维模式**: Temperature=0.6, TopP=0.95, TopK=20
- **非思维模式**: Temperature=0.7, TopP=0.8, TopK=20
- **避免贪婪解码**: 不要使用 Temperature=0
- **Token 数量建议**:
  - 简单问题: 500-1000 tokens
  - 复杂解释: 1000-2000 tokens
  - 数学/编程: 2000+ tokens

## ⚠️ 重要使用提示

### Token 数量建议

**关键发现**: 思维模式需要足够的 tokens 才能正常工作！

- **❌ Token 太少 (50-100)**: 思维内容被截断，没有 `</think>` 结束标签
- **✅ Token 充足 (1000+)**: 思维模式完美工作，内容被正确清理

**推荐设置**:
- 简单问答: 500-1000 tokens
- 思维模式: 1000+ tokens（最小值）
- 复杂解释: 2000+ tokens
- 数学/编程: 2000+ tokens

## 📡 API 接口

### 主要端点

- `POST /api/v1/generate` - 文本生成
- `GET /api/v1/model/info` - 模型信息
- `GET /api/v1/health` - 健康检查
- `POST /api/v1/model/reload` - 重新加载模型

### 直接 API 调用

```bash
# 基本调用
curl -X POST "http://localhost:8000/api/v1/generate" \
     -H "Content-Type: application/json" \
     -d '{"prompt": "你好", "max_tokens": 500}'

# 思维模式（重要：使用足够的 tokens）
curl -X POST "http://localhost:8000/api/v1/generate" \
     -H "Content-Type: application/json" \
     -d '{"prompt": "解释量子计算", "enable_thinking": true, "max_tokens": 2000}'

# 系统提示词
curl -X POST "http://localhost:8000/api/v1/generate" \
     -H "Content-Type: application/json" \
     -d '{"prompt": "写一首诗", "system_prompt": "你是一个诗人", "max_tokens": 800}'
```

### API 参数详细说明

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `prompt` | string | ✅ | 输入提示文本 |
| `system_prompt` | string | ❌ | 系统提示词 |
| `max_tokens` | integer | ❌ | 最大生成token数，**重要**: 思维模式需要 1000+ tokens |
| `temperature` | float | ❌ | 温度参数 (0.0-2.0) |
| `top_p` | float | ❌ | top_p参数 (0.0-1.0) |
| `top_k` | integer | ❌ | top_k参数 (1-100) |
| `enable_thinking` | boolean | ❌ | 是否启用思维模式 |
| `task_type` | string | ❌ | 任务类型 (`math`, `programming`, `multiple_choice`) |

## 🎨 使用示例

### 基本文本生成

```bash
python qwen_api_client.py generate "介绍一下人工智能" --max-tokens 1000
```

### 思维模式（重要：使用足够的 tokens）

```bash
python qwen_api_client.py generate "解释相对论" --thinking --max-tokens 2000
```

### 数学问题

```bash
python qwen_api_client.py generate "解方程 2x + 5 = 15" --task-type math --max-tokens 1500
```

### 编程问题

```bash
python qwen_api_client.py generate "写一个Python快速排序函数" --task-type programming --max-tokens 2000
```

### 创意写作

```bash
python qwen_api_client.py generate "写一个科幻短故事" --system "你是一个科幻作家" --max-tokens 1500 --temperature 0.8
```

## 🧠 显存管理

### 概念说明

- **清理GPU缓存**: 清理PyTorch的GPU缓存，释放显存碎片，模型仍在显存中
- **卸载模型**: 完全从显存中移除模型，释放所有显存，下次使用需重新加载

### API 显存管理选项

```python
import requests

# 生成后清理GPU缓存（推荐日常使用）
response = requests.post("http://localhost:8000/api/v1/generate", json={
    "prompt": "请介绍一下机器学习",
    "max_tokens": 200,
    "clear_cache_after": True  # 清理GPU缓存
})

# 生成后卸载模型（长时间不用时）
response = requests.post("http://localhost:8000/api/v1/generate", json={
    "prompt": "请简单介绍一下深度学习",
    "max_tokens": 150,
    "unload_model_after": True  # 卸载模型
})

# 组合使用
response = requests.post("http://localhost:8000/api/v1/generate", json={
    "prompt": "分析人工智能发展趋势",
    "enable_thinking": True,
    "max_tokens": 300,
    "clear_cache_after": True,
    "temperature": 0.6
})
```

### 使用建议

- **频繁使用**: 不设置显存管理选项，保持最佳性能
- **偶尔使用**: 设置 `clear_cache_after=true`，优化显存使用
- **长时间不用**: 设置 `unload_model_after=true`，完全释放显存
- **显存紧张**: 根据需要组合使用各种选项

### 手动显存管理

```bash
# 清理GPU缓存
curl -X POST "http://localhost:8000/api/v1/model/clear-memory"

# 卸载模型
curl -X POST "http://localhost:8000/api/v1/model/unload"

# 获取显存信息
curl "http://localhost:8000/api/v1/model/memory-info"
```

## 🔧 故障排除

### 常见问题

1. **思维内容没有被清理**
   - **原因**: token 数量不足，模型无法生成完整的 `</think>` 标签
   - **解决**: 增加 `--max-tokens` 到 1000 以上

2. **服务器连接失败**
   ```bash
   # 检查服务器状态
   curl http://localhost:8000/api/v1/health
   ```

3. **模型加载失败**
   ```bash
   # 检查模型文件
   ls -la audio_processor/models/qwen/Qwen3-32B-Q5_0.gguf
   ```

4. **GPU 不可用**
   ```bash
   nvidia-smi
   ```

5. **内存不足**
   - 降低 `n_ctx` 参数 (在 configs/qwen.yaml 中)
   - 减少 `max_tokens` 参数

## 📊 性能参考

### RTX5090 基准数据

| 场景 | Token数量 | 处理时间 | 备注 |
|------|-----------|----------|------|
| 简单问答 | 500 | ~2-3秒 | 足够大多数问题 |
| 思维模式 | 1000 | ~5-10秒 | 推荐最小值 |
| 复杂解释 | 2000 | ~15-25秒 | 详细回答 |
| 数学/编程 | 2000+ | ~20-30秒 | 复杂问题 |

### 系统资源

- **系统内存**: ~10GB
- **GPU 显存**: 优化良好，使用很少
- **GPU 利用率**: 生成时 90%+
- **推荐配置**: 16GB+ 系统内存，RTX3080+ GPU

## 💡 使用提示

1. **首次使用**: 先运行测试脚本验证安装
2. **思维模式**: 复杂推理任务建议启用 `--thinking`，并使用 1000+ tokens
3. **参数调优**:
   - 事实性任务: `temperature=0.1-0.3`
   - 创意性任务: `temperature=0.7-0.9`
4. **性能监控**: 使用 `nvidia-smi` 监控GPU使用情况

## 🔗 相关文件

- **API 文档**: <http://localhost:8000/docs>
- **配置**: `configs/qwen.yaml`
- **测试**: `scripts/test_qwen_api.py`
- **安装**: `scripts/install_api_dependencies.sh`
