# 音频转录时间戳功能指南

本文档详细说明了音频转录系统中的时间戳功能，包括使用方法、已知问题和最佳实践。

## 功能概述

时间戳功能用于在音频转录结果中标记每个文本片段的起止时间，主要有两种粒度：

1. **段级别时间戳（segment）**：标记句子级别的时间戳，稳定可靠
2. **词级别时间戳（word）**：标记单词级别的时间戳，对音频长度有限制

## 使用方法

### 通过代码使用

```python
from audio_processor.transcribing.transcriber import WhisperTranscriber

# 创建转录器实例
transcriber = WhisperTranscriber()

# 段级别时间戳（默认，更稳定）
result = transcriber.transcribe(
    "audio.wav",
    return_timestamps=True,
    timestamp_granularity="segment"
)

# 词级别时间戳（仅适用于短音频）
result = transcriber.transcribe(
    "short_audio.wav",  # 注意：应小于30秒
    return_timestamps=True,
    timestamp_granularity="word"
)

# 输出时间戳信息
if 'chunks' in result:
    for i, chunk in enumerate(result['chunks'][:5]):
        if 'timestamp' in chunk:
            start_time, end_time = chunk['timestamp']
            print(f"{i+1}. '{chunk['text']}' [{start_time:.2f}s - {end_time:.2f}s]")
```

### 通过命令行使用

```bash
# 段级别时间戳（默认）
python -m audio_processor.cli.whisper_cli audio.wav --timestamp-granularity segment

# 词级别时间戳
python -m audio_processor.cli.whisper_cli short_audio.wav --word-timestamps
```

## 已知问题和解决方案

### 1. 词级别时间戳的音频长度限制

**问题**：词级别时间戳功能仅适用于短音频（小于30秒），对于长音频可能会出现以下错误：

```
ValueError: You have passed more than 3000 mel input features (> 30 seconds) which automatically enables long-form generation which requires the model to predict timestamp tokens. Please either pass `return_timestamps=True` or make sure to pass no more than 3000 mel input features.
```

**解决方案**：
- 对于长音频，优先使用段级别时间戳
- 如果必须使用词级别时间戳，可以将长音频分割成小段（<30秒）后处理
- 使用预处理模式，系统会自动分段处理

### 2. 参数设置问题

**注意事项**：
- `max_new_tokens`：不应超过448（Whisper模型限制）
- `num_beams`：必须是整数（不是浮点数）
- 长音频必须启用时间戳功能

**配置示例**（configs/whisper.yaml）：
```yaml
transcription:
  max_new_tokens: 128        # 避免超出限制
  num_beams: 1               # 确保是整数
  return_timestamps: true    # 长音频必须启用
  timestamp_granularity: "segment"  # 默认使用段级别时间戳
```

### 3. 预处理模式与时间戳

预处理模式会自动对音频进行分段，每段都有自己的时间戳信息。在这种模式下：

```python
# 启用预处理的转录
transcriber = WhisperTranscriber(enable_preprocessing=True)
result = transcriber.transcribe("long_audio.wav")

# 时间戳信息在segments中
if 'segments' in result:
    for seg in result['segments'][:5]:
        print(f"[{seg['startTime']}s - {seg['endTime']}s] {seg['sentence'][:50]}...")
```

## 最佳实践

1. **默认使用段级别时间戳**：更稳定，适用于所有长度的音频
2. **词级别时间戳仅用于短音频**：理想情况下小于30秒
3. **长音频使用预处理模式**：自动分段并生成时间戳
4. **检查配置参数**：确保参数设置正确，特别是`max_new_tokens`和`num_beams`

## 技术细节

时间戳功能是通过Whisper模型的内置功能实现的。在内部：

1. 段级别时间戳：`return_timestamps=True`
2. 词级别时间戳：`return_timestamps="word"`

Whisper模型对词级别时间戳有一些限制，主要是为了防止生成过程中的张量维度问题。在长音频中，模型需要生成大量token并同时跟踪每个词的时间位置，这可能导致内存和计算资源不足。

## 故障排除

如果遇到时间戳相关问题：

1. 检查音频长度：词级别时间戳要求音频小于30秒
2. 验证配置参数：确保参数类型和值范围正确
3. 尝试段级别时间戳：作为稳定的替代方案
4. 使用预处理模式：对长音频进行自动分段

如有其他问题，请参考完整的API文档或提交GitHub issue。 