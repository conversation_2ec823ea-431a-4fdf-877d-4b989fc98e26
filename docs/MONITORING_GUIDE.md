# 系统监控功能指南

## 概述

增强的系统监控功能提供了对 Whisper 和 Qwen 模型运行状态的全面监控，包括：

- 系统内存和 GPU 使用情况监控
- 模型生命周期状态跟踪
- 专门的监控日志记录
- 实时资源使用情况报告

## 功能特性

### 1. 模型状态监控

系统可以跟踪以下模型状态：

- **UNLOADED**: 模型未加载
- **LOADING**: 模型正在加载中
- **LOADED**: 模型已加载完成
- **PROCESSING**: 模型正在处理任务
- **UNLOADING**: 模型正在卸载中

### 2. 资源监控

- **系统内存**: 总内存、已用内存、可用内存、使用百分比
- **GPU 显存**: 每个 GPU 的显存使用情况、温度、利用率
- **模型内存**: 每个模型的 GPU 和系统内存占用

### 3. 专门的监控日志

监控信息记录在专门的日志文件中：
- 文件位置: `logs/system_monitor.log`
- 格式: `时间戳 | 级别 | MONITOR | 消息`
- 自动轮转: 10MB 轮转，保留 7 天

## 使用方法

### 基本使用

```python
from audio_processor.utils.monitor import SystemMonitor, ModelType, ModelStatus
from audio_processor.core.config import ConfigManager

# 创建监控器
config_manager = ConfigManager()
config = config_manager.get_config("config")
monitor = SystemMonitor(config)

# 启动监控
monitor.start_monitoring()

# 更新模型状态
monitor.update_model_status(ModelType.WHISPER, ModelStatus.LOADING)

# 获取当前状态
status = monitor.get_current_status()

# 停止监控
monitor.stop_monitoring()
```

### 在模型处理器中集成

模型处理器已经自动集成了监控功能：

#### Whisper 转录器
```python
from audio_processor.transcribing.transcriber import WhisperTranscriber

# 创建转录器（自动启用监控）
transcriber = WhisperTranscriber()

# 转录过程中会自动更新状态：
# LOADING -> LOADED -> PROCESSING -> LOADED
result = transcriber.transcribe("audio.wav")
```

#### Qwen 处理器
```python
from audio_processor.models.qwen.processor import QwenProcessor

# 创建处理器（自动启用监控）
processor = QwenProcessor()

# 生成过程中会自动更新状态：
# LOADING -> LOADED -> PROCESSING -> LOADED
result = processor.generate_text("你好")
```

## 监控日志示例

```
2025-05-27 07:12:25 | INFO | MONITOR | 模型状态变化: whisper unloaded -> loading
2025-05-27 07:12:27 | INFO | MONITOR | 模型状态变化: whisper loading -> loaded
2025-05-27 07:12:27 | INFO | MONITOR | 模型内存使用: whisper GPU=2.80GB, 系统=1.50GB
2025-05-27 07:12:30 | INFO | MONITOR | === 系统状态 ===
2025-05-27 07:12:30 | INFO | MONITOR | 内存使用: 11.4GB / 62.6GB (20.0%)
2025-05-27 07:12:30 | INFO | MONITOR | 可用内存: 50.1GB
2025-05-27 07:12:30 | INFO | MONITOR | GPU 0 (NVIDIA GeForce RTX 5090): 显存 0.0GB / 31.4GB (0.0%), 利用率 16%, 温度 47°C
2025-05-27 07:12:30 | INFO | MONITOR | === 模型状态 ===
2025-05-27 07:12:30 | INFO | MONITOR | WHISPER: loaded (GPU: 2.80GB, 系统: 1.50GB)
2025-05-27 07:12:30 | INFO | MONITOR | QWEN: unloaded
```

## API 参考

### SystemMonitor 类

#### 主要方法

- `start_monitoring()`: 启动监控
- `stop_monitoring()`: 停止监控
- `update_model_status(model_type, status, memory_info=None)`: 更新模型状态
- `get_model_status(model_type)`: 获取模型状态
- `get_current_status()`: 获取当前系统状态
- `clear_gpu_cache()`: 清理 GPU 缓存

#### 状态信息结构

```python
{
    "timestamp": "2025-05-27 07:12:30",
    "memory": {
        "total": 62.6,
        "used": 11.4,
        "available": 50.1,
        "percent": 20.0
    },
    "gpu": {
        "available": True,
        "devices": [
            {
                "id": 0,
                "name": "NVIDIA GeForce RTX 5090",
                "memory_allocated": 0.0,
                "memory_total": 31.4,
                "memory_percent": 0.0,
                "temperature": 47,
                "utilization": 16
            }
        ]
    },
    "models": {
        "whisper": {
            "status": "loaded",
            "memory_usage": {
                "gpu_memory_allocated": 2.8,
                "system_memory_used": 1.5
            }
        },
        "qwen": {
            "status": "unloaded",
            "memory_usage": {}
        }
    }
}
```

### ModelMonitorMixin 类

为模型处理器提供监控功能的混入类：

- `_setup_model_monitoring(monitor, model_type)`: 设置监控
- `_update_model_status(status, memory_info=None)`: 更新状态
- `_get_current_memory_info()`: 获取当前内存信息

## 配置选项

在 `configs/config.yaml` 中配置监控参数：

```yaml
monitoring:
  enable_memory_monitoring: true
  enable_gpu_monitoring: true
  log_interval: 10  # 监控日志间隔（秒）

paths:
  logs_dir: "logs"  # 日志目录
```

## 演示脚本

运行演示脚本查看监控功能：

```bash
python demo_monitoring.py
```

这将展示完整的监控功能，包括模型状态变化和资源使用情况。

## 故障排除

### 常见问题

1. **监控日志文件未创建**
   - 检查 `logs` 目录是否存在
   - 确保有写入权限

2. **GPU 信息显示不正确**
   - 安装 `gpustat`: `pip install gpustat`
   - 检查 NVIDIA 驱动是否正常

3. **内存使用显示为 0**
   - 检查 `psutil` 是否正确安装
   - 确保 PyTorch 正确检测到 GPU

### 日志级别

监控日志默认级别为 INFO，可以通过修改配置调整：

```yaml
logging:
  level: "DEBUG"  # 更详细的日志
```
